import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/errors/failures.dart';

/// Type definition for a usecase that returns a Future with Either type
typedef ResultFuture<T> = Future<Either<Failure, T>>;

/// Type definition for a usecase that returns a Future with Either type for void return
typedef ResultVoid = Future<Either<Failure, void>>;

/// Base usecase abstract class that all usecases should extend
abstract class UseCase<Type, Params> {
  ResultFuture<Type> call(Params params);
}

/// No params class for usecases that don't need parameters
class NoParams extends Equatable {
  @override
  List<Object?> get props => [];
}
