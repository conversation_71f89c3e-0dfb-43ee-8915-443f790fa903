import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/routes/app_routes.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          // Data Management Section
          _buildSectionHeader(context, 'إدارة البيانات'),
          _buildSettingsTile(
            context: context,
            icon: Icons.category,
            title: 'إدارة الفئات',
            subtitle: 'إضافة وتعديل وحذف الفئات',
            onTap: () => context.push('${AppRoutes.settings}/categories'),
          ),
          _buildSettingsTile(
            context: context,
            icon: Icons.monetization_on,
            title: 'إدارة العملات',
            subtitle: 'إضافة وتعديل وحذف العملات',
            onTap: () => context.push('${AppRoutes.settings}/currencies'),
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Backup & Restore Section
          _buildSectionHeader(context, 'النسخ الاحتياطي'),
          _buildSettingsTile(
            context: context,
            icon: Icons.backup,
            title: 'إنشاء نسخة احتياطية',
            subtitle: 'حفظ البيانات في ملف',
            onTap: () => _showComingSoon(context),
          ),
          _buildSettingsTile(
            context: context,
            icon: Icons.restore,
            title: 'استعادة البيانات',
            subtitle: 'استيراد البيانات من ملف',
            onTap: () => _showComingSoon(context),
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // App Settings Section
          _buildSectionHeader(context, 'إعدادات التطبيق'),
          _buildSettingsTile(
            context: context,
            icon: Icons.notifications,
            title: 'الإشعارات',
            subtitle: 'إعدادات التذكيرات والإشعارات',
            onTap: () => _showComingSoon(context),
          ),
          _buildSettingsTile(
            context: context,
            icon: Icons.security,
            title: 'الأمان والخصوصية',
            subtitle: 'قفل التطبيق وحماية البيانات',
            onTap: () => _showComingSoon(context),
          ),
          _buildSettingsTile(
            context: context,
            icon: Icons.language,
            title: 'اللغة',
            subtitle: 'العربية',
            onTap: () => _showComingSoon(context),
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // About Section
          _buildSectionHeader(context, 'حول التطبيق'),
          _buildSettingsTile(
            context: context,
            icon: Icons.info,
            title: 'معلومات التطبيق',
            subtitle: 'الإصدار ${AppConstants.appVersion}',
            onTap: () => _showAboutDialog(context),
          ),
          _buildSettingsTile(
            context: context,
            icon: Icons.help,
            title: 'المساعدة والدعم',
            subtitle: 'كيفية استخدام التطبيق',
            onTap: () => _showComingSoon(context),
          ),
          _buildSettingsTile(
            context: context,
            icon: Icons.star,
            title: 'تقييم التطبيق',
            subtitle: 'شاركنا رأيك في التطبيق',
            onTap: () => _showComingSoon(context),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppConstants.smallPadding,
        top: AppConstants.smallPadding,
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }

  Widget _buildSettingsTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(AppConstants.smallPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius / 2),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  void _showComingSoon(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('قريباً'),
        content: const Text('هذه الميزة ستكون متاحة في التحديثات القادمة.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: const Icon(
        Icons.account_balance_wallet,
        size: 48,
      ),
      children: [
        const Text(
          'تطبيق لإدارة الديون والحسابات الشخصية مع دعم العملات المتعددة والفئات المخصصة.',
        ),
        const SizedBox(height: 16),
        const Text(
          'تم تطويره باستخدام Flutter مع دعم كامل للغة العربية.',
        ),
      ],
    );
  }
}
