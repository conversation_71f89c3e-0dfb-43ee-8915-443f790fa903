import '../../domain/entities/currency.dart';

/// Currency model that extends the Currency entity
class CurrencyModel extends Currency {
  const CurrencyModel({
    super.id,
    required super.name,
    required super.code,
    required super.symbol,
    super.isDefault = false,
    super.createdAt,
    super.updatedAt,
  });

  /// Create a CurrencyModel from a map (e.g., from database or API)
  factory CurrencyModel.fromMap(Map<String, dynamic> map) {
    return CurrencyModel(
      id: map['id'],
      name: map['name'],
      code: map['code'],
      symbol: map['symbol'],
      isDefault: map['is_default'] == 1,
      createdAt:
          map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  /// Convert this CurrencyModel to a map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'code': code,
      'symbol': symbol,
      'is_default': isDefault ? 1 : 0,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Create a copy of this CurrencyModel with the given fields replaced with the new values.
  @override
  CurrencyModel copyWith({
    int? id,
    String? name,
    String? code,
    String? symbol,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CurrencyModel(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      symbol: symbol ?? this.symbol,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
