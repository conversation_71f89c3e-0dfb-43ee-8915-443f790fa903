import 'package:equatable/equatable.dart';
import '../../../domain/entities/currency.dart';

/// Base class for all currency events
abstract class CurrencyEvent extends Equatable {
  const CurrencyEvent();

  @override
  List<Object?> get props => [];
}

/// Event to get all currencies
class GetCurrenciesEvent extends CurrencyEvent {
  const GetCurrenciesEvent();
}

/// Event to create a new currency
class CreateCurrencyEvent extends CurrencyEvent {
  final Currency currency;

  const CreateCurrencyEvent(this.currency);

  @override
  List<Object?> get props => [currency];
}

/// Event to update an existing currency
class UpdateCurrencyEvent extends CurrencyEvent {
  final Currency currency;

  const UpdateCurrencyEvent(this.currency);

  @override
  List<Object?> get props => [currency];
}

/// Event to delete a currency
class DeleteCurrencyEvent extends CurrencyEvent {
  final int currencyId;

  const DeleteCurrencyEvent(this.currencyId);

  @override
  List<Object?> get props => [currencyId];
}

/// Event to search currencies
class SearchCurrenciesEvent extends CurrencyEvent {
  final String query;

  const SearchCurrenciesEvent(this.query);

  @override
  List<Object?> get props => [query];
}

/// Event to clear currency search
class ClearCurrencySearchEvent extends CurrencyEvent {
  const ClearCurrencySearchEvent();
}

/// Event to select a currency
class SelectCurrencyEvent extends CurrencyEvent {
  final Currency? currency;

  const SelectCurrencyEvent(this.currency);

  @override
  List<Object?> get props => [currency];
}
