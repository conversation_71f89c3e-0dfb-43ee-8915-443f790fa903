import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/account.dart';

/// Repository interface for Account operations
abstract class AccountRepository {
  /// Get all accounts
  Future<Either<Failure, List<Account>>> getAccounts({
    int? categoryId,
    int? currencyId,
  });

  /// Get a single account by ID
  Future<Either<Failure, Account>> getAccountById(int id);

  /// Create a new account
  Future<Either<Failure, Account>> createAccount(Account account);

  /// Update an existing account
  Future<Either<Failure, Account>> updateAccount(Account account);

  /// Delete an account
  Future<Either<Failure, void>> deleteAccount(int id);

  /// Search accounts by name or phone
  Future<Either<Failure, List<Account>>> searchAccounts(String query);
}
