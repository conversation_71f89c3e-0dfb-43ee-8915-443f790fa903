import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import '../../../core/errors/failures.dart';
import '../../../core/utils/logger.dart';

/// Helper class for database operations
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;
  static const _maxRetries = 3;

  /// Factory constructor
  factory DatabaseHelper() => _instance;

  /// Internal constructor
  DatabaseHelper._internal();

  /// Get database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabaseWithRetry();
    return _database!;
  }

  /// Initialize database with retry mechanism
  Future<Database> _initDatabaseWithRetry() async {
    int retryCount = 0;

    while (retryCount < _maxRetries) {
      try {
        Logger.debug('Initializing database (attempt ${retryCount + 1})');
        return await _initDatabase();
      } catch (e, stackTrace) {
        retryCount++;
        Logger.warning(
          'Database initialization failed (attempt $retryCount/$_maxRetries): $e',
        );

        if (retryCount >= _maxRetries) {
          Logger.error(
            'Failed to initialize database after $_maxRetries attempts',
            e,
            stackTrace,
          );
          throw DatabaseFailure(
            'فشل في تهيئة قاعدة البيانات بعد $_maxRetries محاولات: $e',
          );
        }

        // Wait before retrying
        await Future.delayed(Duration(milliseconds: 500 * retryCount));
      }
    }

    // This should never be reached due to the throw in the catch block
    throw DatabaseFailure('فشل غير متوقع في تهيئة قاعدة البيانات');
  }

  /// Initialize the database
  Future<Database> _initDatabase() async {
    // Initialize platform-specific database factory
    if (kIsWeb) {
      // Web platform doesn't support SQLite directly
      throw DatabaseFailure(
        'قاعدة البيانات غير مدعومة على منصة الويب. يرجى استخدام تطبيق الهاتف المحمول.',
      );
    }

    // Get the database path
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, 'accounts.db');

    Logger.debug('Database path: $path');

    // Open the database
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      onDowngrade: _onDowngrade,
    );
  }

  /// Create database tables
  Future<void> _onCreate(Database db, int version) async {
    Logger.debug('Creating database tables (version $version)');

    try {
      // Create categories table
      await db.execute('''
        CREATE TABLE categories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL UNIQUE,
          is_default INTEGER DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      ''');

      // Create currencies table
      await db.execute('''
        CREATE TABLE currencies (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          code TEXT NOT NULL UNIQUE,
          symbol TEXT NOT NULL,
          is_default INTEGER DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      ''');

      // Create accounts table
      await db.execute('''
        CREATE TABLE accounts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          phone TEXT,
          amount REAL NOT NULL,
          details TEXT,
          debt_type TEXT NOT NULL CHECK (debt_type IN ('له', 'عليه')),
          date TEXT NOT NULL,
          category_id INTEGER NOT NULL,
          currency_id INTEGER NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (category_id) REFERENCES categories (id),
          FOREIGN KEY (currency_id) REFERENCES currencies (id)
        )
      ''');

      // Insert default categories
      await _insertDefaultCategories(db);

      // Insert default currencies
      await _insertDefaultCurrencies(db);

      Logger.debug('Database tables created successfully');
    } catch (e, stackTrace) {
      Logger.error('Error creating database tables', e, stackTrace);
      throw DatabaseFailure('فشل في إنشاء جداول قاعدة البيانات: $e');
    }
  }

  /// Insert default categories
  Future<void> _insertDefaultCategories(Database db) async {
    try {
      await db.insert('categories', {'name': 'عام', 'is_default': 1});

      await db.insert('categories', {'name': 'أصدقاء', 'is_default': 0});

      await db.insert('categories', {'name': 'عائلة', 'is_default': 0});

      await db.insert('categories', {'name': 'عمل', 'is_default': 0});

      await db.insert('categories', {'name': 'شخصي', 'is_default': 0});

      Logger.debug('Default categories inserted successfully');
    } catch (e, stackTrace) {
      Logger.error('Error inserting default categories', e, stackTrace);
      throw DatabaseFailure('فشل في إدراج الفئات الافتراضية: $e');
    }
  }

  /// Insert default currencies
  Future<void> _insertDefaultCurrencies(Database db) async {
    try {
      await db.insert('currencies', {
        'name': 'ريال سعودي',
        'code': 'SAR',
        'symbol': '﷼',
        'is_default': 1,
      });

      await db.insert('currencies', {
        'name': 'دولار أمريكي',
        'code': 'USD',
        'symbol': '\$',
        'is_default': 0,
      });

      await db.insert('currencies', {
        'name': 'يورو',
        'code': 'EUR',
        'symbol': '€',
        'is_default': 0,
      });

      await db.insert('currencies', {
        'name': 'جنيه استرليني',
        'code': 'GBP',
        'symbol': '£',
        'is_default': 0,
      });

      await db.insert('currencies', {
        'name': 'درهم إماراتي',
        'code': 'AED',
        'symbol': 'د.إ',
        'is_default': 0,
      });

      Logger.debug('Default currencies inserted successfully');
    } catch (e, stackTrace) {
      Logger.error('Error inserting default currencies', e, stackTrace);
      throw DatabaseFailure('فشل في إدراج العملات الافتراضية: $e');
    }
  }

  /// Handle database upgrade
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    Logger.debug('Upgrading database from version $oldVersion to $newVersion');
    // Add migration logic here when needed
  }

  /// Handle database downgrade
  Future<void> _onDowngrade(Database db, int oldVersion, int newVersion) async {
    Logger.debug(
      'Downgrading database from version $oldVersion to $newVersion',
    );
    // Add downgrade logic here when needed
  }

  /// Close the database
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      Logger.debug('Database closed');
    }
  }
}
