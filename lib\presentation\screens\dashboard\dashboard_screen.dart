import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/di/service_locator.dart';
import '../../../domain/entities/account.dart';
import '../../bloc/account/account_bloc.dart';
import '../../bloc/account/account_event.dart';
import '../../bloc/account/account_state.dart';
import '../../bloc/category/category_bloc.dart';
import '../../bloc/category/category_event.dart';
import '../../bloc/category/category_state.dart';
import '../../bloc/currency/currency_bloc.dart';
import '../../bloc/currency/currency_event.dart';
import '../../bloc/currency/currency_state.dart';
import '../../widgets/account/account_card.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedCategoryIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 1, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<CategoryBloc>(
          create:
              (context) =>
                  ServiceLocator.get<CategoryBloc>()
                    ..add(const GetCategoriesEvent()),
        ),
        BlocProvider<CurrencyBloc>(
          create:
              (context) =>
                  ServiceLocator.get<CurrencyBloc>()
                    ..add(const GetCurrenciesEvent()),
        ),
      ],
      child: Scaffold(
        appBar: AppBar(
          title: const Text(AppConstants.appName),
          actions: [
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => context.push(AppRoutes.search),
              tooltip: 'البحث',
            ),
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () => context.push(AppRoutes.settings),
              tooltip: 'الإعدادات',
            ),
          ],
        ),
        body: Column(
          children: [
            // Statistics Overview
            _buildStatisticsOverview(),

            // Category Tabs
            _buildCategoryTabs(),

            // Accounts List
            Expanded(child: _buildAccountsList()),
          ],
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => context.push(AppRoutes.accountForm),
          tooltip: 'إضافة حساب جديد',
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildStatisticsOverview() {
    return BlocBuilder<AccountBloc, AccountState>(
      builder: (context, state) {
        if (state.status != AccountStatus.success) {
          return const SizedBox.shrink();
        }

        final owedToMe = state.accounts
            .where(
              (account) => account.debtType == AppConstants.debtTypeOwedToMe,
            )
            .fold(0.0, (sum, account) => sum + account.amount);

        final owedByMe = state.accounts
            .where(
              (account) => account.debtType == AppConstants.debtTypeOwedByMe,
            )
            .fold(0.0, (sum, account) => sum + account.amount);

        final netBalance = owedToMe - owedByMe;

        return Container(
          margin: const EdgeInsets.all(AppConstants.defaultPadding),
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'لي',
                  owedToMe,
                  Colors.green,
                  Icons.arrow_upward,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _buildStatCard(
                  'عليّ',
                  owedByMe,
                  Colors.red,
                  Icons.arrow_downward,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _buildStatCard(
                  'الصافي',
                  netBalance,
                  netBalance >= 0 ? Colors.green : Colors.red,
                  netBalance >= 0 ? Icons.trending_up : Icons.trending_down,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(
    String title,
    double amount,
    Color color,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(title, style: Theme.of(context).textTheme.bodySmall),
        const SizedBox(height: 4),
        Text(
          amount.toStringAsFixed(2),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryTabs() {
    return BlocBuilder<CategoryBloc, CategoryState>(
      builder: (context, state) {
        if (state.status == CategoryStatus.loading) {
          return const SizedBox(
            height: 60,
            child: Center(child: CircularProgressIndicator()),
          );
        }

        if (state.status == CategoryStatus.failure) {
          return Container(
            height: 60,
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Center(
              child: Text(
                state.errorMessage ?? 'خطأ في تحميل الفئات',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ),
          );
        }

        if (state.categories.isEmpty) {
          return const SizedBox.shrink();
        }

        // Update tab controller length
        if (_tabController.length != state.categories.length) {
          _tabController.dispose();
          _tabController = TabController(
            length: state.categories.length,
            vsync: this,
            initialIndex: _selectedCategoryIndex.clamp(
              0,
              state.categories.length - 1,
            ),
          );
          _tabController.addListener(() {
            if (_tabController.indexIsChanging) {
              setState(() {
                _selectedCategoryIndex = _tabController.index;
              });
              _filterAccountsByCategory();
            }
          });
        }

        return Container(
          height: 60,
          child: TabBar(
            controller: _tabController,
            isScrollable: true,
            tabs:
                state.categories.map((category) {
                  return Tab(
                    text: category.name,
                    icon:
                        category.isDefault
                            ? const Icon(Icons.home, size: 16)
                            : const Icon(Icons.category, size: 16),
                  );
                }).toList(),
          ),
        );
      },
    );
  }

  Widget _buildAccountsList() {
    return BlocBuilder<AccountBloc, AccountState>(
      builder: (context, state) {
        if (state.status == AccountStatus.loading) {
          return const LoadingWidget();
        }

        if (state.status == AccountStatus.failure) {
          return CustomErrorWidget(
            message: state.errorMessage ?? AppConstants.genericErrorMessage,
            onRetry: () {
              context.read<AccountBloc>().add(const GetAccountsEvent());
            },
          );
        }

        if (state.filteredAccounts.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.account_balance_wallet_outlined,
                  size: 64,
                  color: Theme.of(context).colorScheme.outline,
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  'لا توجد حسابات',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  'أضف حساب جديد باستخدام الزر أدناه',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            context.read<AccountBloc>().add(const GetAccountsEvent());
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.smallPadding),
            itemCount: state.filteredAccounts.length,
            itemBuilder: (context, index) {
              final account = state.filteredAccounts[index];
              return AccountCard(
                account: account,
                onTap: () => _navigateToAccountDetails(account),
                onEdit: () => _navigateToAccountEdit(account),
                onDelete: () => _showDeleteConfirmation(account),
              );
            },
          ),
        );
      },
    );
  }

  void _filterAccountsByCategory() {
    final categoryBloc = context.read<CategoryBloc>();
    final accountBloc = context.read<AccountBloc>();

    if (categoryBloc.state.categories.isNotEmpty &&
        _selectedCategoryIndex < categoryBloc.state.categories.length) {
      final selectedCategory =
          categoryBloc.state.categories[_selectedCategoryIndex];
      accountBloc.add(FilterAccountsEvent(categoryId: selectedCategory.id));
    }
  }

  void _navigateToAccountDetails(Account account) {
    context.push('${AppRoutes.accountDetails}/${account.id}');
  }

  void _navigateToAccountEdit(Account account) {
    context.push(AppRoutes.accountForm, extra: account);
  }

  void _showDeleteConfirmation(Account account) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل أنت متأكد من حذف حساب "${account.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<AccountBloc>().add(
                    DeleteAccountEvent(id: account.id!),
                  );
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }
}
