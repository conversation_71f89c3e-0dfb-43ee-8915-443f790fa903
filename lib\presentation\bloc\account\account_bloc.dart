import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/errors/failures.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/account.dart';
import '../../../domain/usecases/account/create_account.dart';
import '../../../domain/usecases/account/delete_account.dart';
import '../../../domain/usecases/account/get_accounts.dart';
import '../../../domain/usecases/account/search_accounts.dart';
import '../../../domain/usecases/account/update_account.dart';
import 'account_event.dart';
import 'account_state.dart';

/// Base BLoC with advanced error handling
abstract class BaseBloc<Event, State> extends Bloc<Event, State> {
  BaseBloc(super.initialState);

  @override
  void onError(Object error, StackTrace stackTrace) {
    Logger.error('BLoC Error: $error', error, stackTrace);
    super.onError(error, stackTrace);
  }

  @override
  void onTransition(Transition<Event, State> transition) {
    Logger.debug('BLoC Transition: $transition');
    super.onTransition(transition);
  }
}

/// Account BLoC that manages account state
class AccountBloc extends BaseBloc<AccountEvent, AccountState> {
  final CreateAccount _createAccount;
  final GetAccounts _getAccounts;
  final UpdateAccount _updateAccount;
  final DeleteAccount _deleteAccount;
  final SearchAccounts _searchAccounts;

  // Retry mechanism
  static const int _maxRetries = 3;
  int _getAccountsRetryCount = 0;

  AccountBloc({
    required CreateAccount createAccount,
    required GetAccounts getAccounts,
    required UpdateAccount updateAccount,
    required DeleteAccount deleteAccount,
    required SearchAccounts searchAccounts,
  }) : _createAccount = createAccount,
       _getAccounts = getAccounts,
       _updateAccount = updateAccount,
       _deleteAccount = deleteAccount,
       _searchAccounts = searchAccounts,
       super(const AccountState.initial()) {
    on<GetAccountsEvent>(_onGetAccounts);
    on<CreateAccountEvent>(_onCreateAccount);
    on<UpdateAccountEvent>(_onUpdateAccount);
    on<DeleteAccountEvent>(_onDeleteAccount);
    on<SearchAccountsEvent>(_onSearchAccounts);
    on<FilterAccountsEvent>(_onFilterAccounts);
  }

  Future<void> _onGetAccounts(
    GetAccountsEvent event,
    Emitter<AccountState> emit,
  ) async {
    emit(state.copyWith(status: AccountStatus.loading));

    try {
      final result = await _getAccounts(
        GetAccountsParams(
          categoryId: event.categoryId,
          currencyId: event.currencyId,
        ),
      );

      result.fold(
        (failure) {
          // Retry logic for database failures
          if (failure is DatabaseFailure &&
              _getAccountsRetryCount < _maxRetries) {
            _getAccountsRetryCount++;
            Logger.warning(
              'Retrying getAccounts ($_getAccountsRetryCount/$_maxRetries)',
            );

            // Add the event back to the event queue after a delay
            Future.delayed(
              Duration(milliseconds: 500 * _getAccountsRetryCount),
              () {
                add(event);
              },
            );

            // Keep the loading state
            emit(state.copyWith(status: AccountStatus.loading));
          } else {
            emit(
              state.copyWith(
                status: AccountStatus.failure,
                errorMessage: _mapFailureToMessage(failure),
              ),
            );
            _getAccountsRetryCount = 0;
          }
        },
        (accounts) {
          emit(
            state.copyWith(
              status: AccountStatus.success,
              accounts: accounts,
              filteredAccounts: accounts,
              clearErrorMessage: true,
            ),
          );
          _getAccountsRetryCount = 0;
        },
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in getAccounts', e, stackTrace);
      emit(
        state.copyWith(
          status: AccountStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onCreateAccount(
    CreateAccountEvent event,
    Emitter<AccountState> emit,
  ) async {
    emit(state.copyWith(status: AccountStatus.loading));

    try {
      final result = await _createAccount(event.account);

      result.fold(
        (failure) => emit(
          state.copyWith(
            status: AccountStatus.failure,
            errorMessage: _mapFailureToMessage(failure),
          ),
        ),
        (account) {
          final updatedAccounts = List<Account>.from(state.accounts)
            ..add(account);
          emit(
            state.copyWith(
              status: AccountStatus.success,
              accounts: updatedAccounts,
              filteredAccounts: _applyFilters(
                updatedAccounts,
                state.currentFilter,
              ),
              clearErrorMessage: true,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in createAccount', e, stackTrace);
      emit(
        state.copyWith(
          status: AccountStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onUpdateAccount(
    UpdateAccountEvent event,
    Emitter<AccountState> emit,
  ) async {
    emit(state.copyWith(status: AccountStatus.loading));

    try {
      final result = await _updateAccount(event.account);

      result.fold(
        (failure) => emit(
          state.copyWith(
            status: AccountStatus.failure,
            errorMessage: _mapFailureToMessage(failure),
          ),
        ),
        (updatedAccount) {
          final updatedAccounts =
              state.accounts.map((account) {
                return account.id == updatedAccount.id
                    ? updatedAccount
                    : account;
              }).toList();

          emit(
            state.copyWith(
              status: AccountStatus.success,
              accounts: updatedAccounts,
              filteredAccounts: _applyFilters(
                updatedAccounts,
                state.currentFilter,
              ),
              clearErrorMessage: true,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in updateAccount', e, stackTrace);
      emit(
        state.copyWith(
          status: AccountStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onDeleteAccount(
    DeleteAccountEvent event,
    Emitter<AccountState> emit,
  ) async {
    emit(state.copyWith(status: AccountStatus.loading));

    try {
      final result = await _deleteAccount(DeleteAccountParams(id: event.id));

      result.fold(
        (failure) => emit(
          state.copyWith(
            status: AccountStatus.failure,
            errorMessage: _mapFailureToMessage(failure),
          ),
        ),
        (_) {
          final updatedAccounts =
              state.accounts
                  .where((account) => account.id != event.id)
                  .toList();
          emit(
            state.copyWith(
              status: AccountStatus.success,
              accounts: updatedAccounts,
              filteredAccounts: _applyFilters(
                updatedAccounts,
                state.currentFilter,
              ),
              clearErrorMessage: true,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in deleteAccount', e, stackTrace);
      emit(
        state.copyWith(
          status: AccountStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onSearchAccounts(
    SearchAccountsEvent event,
    Emitter<AccountState> emit,
  ) async {
    emit(
      state.copyWith(status: AccountStatus.loading, searchQuery: event.query),
    );

    try {
      if (event.query.isEmpty) {
        emit(
          state.copyWith(
            status: AccountStatus.success,
            filteredAccounts: _applyFilters(
              state.accounts,
              state.currentFilter,
            ),
            clearErrorMessage: true,
          ),
        );
        return;
      }

      final result = await _searchAccounts(
        SearchAccountsParams(query: event.query),
      );

      result.fold(
        (failure) => emit(
          state.copyWith(
            status: AccountStatus.failure,
            errorMessage: _mapFailureToMessage(failure),
          ),
        ),
        (accounts) => emit(
          state.copyWith(
            status: AccountStatus.success,
            filteredAccounts: _applyFilters(accounts, state.currentFilter),
            clearErrorMessage: true,
          ),
        ),
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in searchAccounts', e, stackTrace);
      emit(
        state.copyWith(
          status: AccountStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  void _onFilterAccounts(
    FilterAccountsEvent event,
    Emitter<AccountState> emit,
  ) {
    try {
      final filter = AccountFilter(
        categoryId: event.categoryId,
        currencyId: event.currencyId,
        debtType: event.debtType,
        startDate: event.startDate,
        endDate: event.endDate,
        minAmount: event.minAmount,
        maxAmount: event.maxAmount,
      );

      final filteredAccounts = _applyFilters(state.accounts, filter);

      emit(
        state.copyWith(
          status: AccountStatus.success,
          filteredAccounts: filteredAccounts,
          currentFilter: filter,
          clearErrorMessage: true,
        ),
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in filterAccounts', e, stackTrace);
      emit(
        state.copyWith(
          status: AccountStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  List<Account> _applyFilters(List<Account> accounts, AccountFilter? filter) {
    if (filter == null) {
      return accounts;
    }

    return accounts.where((account) {
      // Filter by category
      if (filter.categoryId != null &&
          account.categoryId != filter.categoryId) {
        return false;
      }

      // Filter by currency
      if (filter.currencyId != null &&
          account.currencyId != filter.currencyId) {
        return false;
      }

      // Filter by debt type
      if (filter.debtType != null && account.debtType != filter.debtType) {
        return false;
      }

      // Filter by date range
      if (filter.startDate != null || filter.endDate != null) {
        final accountDate = DateTime.parse(account.date);

        if (filter.startDate != null &&
            accountDate.isBefore(filter.startDate!)) {
          return false;
        }

        if (filter.endDate != null && accountDate.isAfter(filter.endDate!)) {
          return false;
        }
      }

      // Filter by amount range
      if (filter.minAmount != null && account.amount < filter.minAmount!) {
        return false;
      }

      if (filter.maxAmount != null && account.amount > filter.maxAmount!) {
        return false;
      }

      return true;
    }).toList();
  }

  String _mapFailureToMessage(Failure failure) {
    if (failure is DatabaseFailure) {
      return 'خطأ في قاعدة البيانات: ${failure.message}';
    } else if (failure is ValidationFailure) {
      return 'خطأ في التحقق من البيانات';
    } else if (failure is NetworkFailure) {
      return 'خطأ في الشبكة: ${failure.message}';
    } else {
      return 'حدث خطأ غير متوقع';
    }
  }
}
