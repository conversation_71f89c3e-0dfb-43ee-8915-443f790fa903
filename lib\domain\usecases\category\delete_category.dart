import 'package:dartz/dartz.dart';
import '../../repositories/category_repository.dart';
import '../../../core/errors/failures.dart';
import '../base/usecase.dart';

/// Parameters for deleting a category
class DeleteCategoryParams {
  final int categoryId;

  DeleteCategoryParams({required this.categoryId});
}

/// Use case for deleting a category
class DeleteCategory implements UseCase<void, DeleteCategoryParams> {
  final CategoryRepository repository;

  DeleteCategory(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteCategoryParams params) async {
    return await repository.deleteCategory(params.categoryId);
  }
}
