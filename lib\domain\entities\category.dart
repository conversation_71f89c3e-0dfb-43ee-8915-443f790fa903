import 'package:equatable/equatable.dart';

/// Category entity representing an account category
class Category extends Equatable {
  final int? id;
  final String name;
  final bool isDefault;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Category({
    this.id,
    required this.name,
    this.isDefault = false,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [id, name, isDefault, createdAt, updatedAt];

  /// Create a copy of this Category with the given fields replaced with the new values.
  Category copyWith({
    int? id,
    String? name,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
