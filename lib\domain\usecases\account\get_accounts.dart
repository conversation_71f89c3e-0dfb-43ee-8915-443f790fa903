import 'package:equatable/equatable.dart';
import '../../../core/errors/failures.dart';
import 'package:dartz/dartz.dart';
import '../../entities/account.dart';
import '../../repositories/account_repository.dart';
import '../base/usecase.dart';

/// Use case for getting accounts with optional filtering
class GetAccounts implements UseCase<List<Account>, GetAccountsParams> {
  final AccountRepository _repository;

  GetAccounts(this._repository);

  @override
  Future<Either<Failure, List<Account>>> call(GetAccountsParams params) async {
    return await _repository.getAccounts(
      categoryId: params.categoryId,
      currencyId: params.currencyId,
    );
  }
}

/// Parameters for the GetAccounts use case
class GetAccountsParams extends Equatable {
  final int? categoryId;
  final int? currencyId;

  const GetAccountsParams({this.categoryId, this.currencyId});

  @override
  List<Object?> get props => [categoryId, currencyId];
}
