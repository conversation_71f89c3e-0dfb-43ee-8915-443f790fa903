import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/di/service_locator.dart';
import '../../../domain/entities/account.dart';
import '../../../domain/entities/category.dart';
import '../../../domain/entities/currency.dart';
import '../../bloc/account/account_bloc.dart';
import '../../bloc/account/account_event.dart';
import '../../bloc/account/account_state.dart';
import '../../bloc/category/category_bloc.dart';
import '../../bloc/category/category_event.dart';
import '../../bloc/category/category_state.dart';
import '../../bloc/currency/currency_bloc.dart';
import '../../bloc/currency/currency_event.dart';
import '../../bloc/currency/currency_state.dart';
import '../../widgets/common/loading_widget.dart';

class AccountFormScreen extends StatefulWidget {
  final Account? account;

  const AccountFormScreen({
    super.key,
    this.account,
  });

  @override
  State<AccountFormScreen> createState() => _AccountFormScreenState();
}

class _AccountFormScreenState extends State<AccountFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _amountController = TextEditingController();
  final _detailsController = TextEditingController();
  
  String _selectedDebtType = AppConstants.debtTypeOwedToMe;
  Category? _selectedCategory;
  Currency? _selectedCurrency;
  DateTime _selectedDate = DateTime.now();

  bool get _isEditing => widget.account != null;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (_isEditing) {
      final account = widget.account!;
      _nameController.text = account.name;
      _phoneController.text = account.phone ?? '';
      _amountController.text = account.amount.toString();
      _detailsController.text = account.details ?? '';
      _selectedDebtType = account.debtType;
      _selectedDate = DateTime.parse(account.date);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _amountController.dispose();
    _detailsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<CategoryBloc>(
          create: (context) => ServiceLocator.get<CategoryBloc>()
            ..add(const GetCategoriesEvent()),
        ),
        BlocProvider<CurrencyBloc>(
          create: (context) => ServiceLocator.get<CurrencyBloc>()
            ..add(const GetCurrenciesEvent()),
        ),
      ],
      child: Scaffold(
        appBar: AppBar(
          title: Text(_isEditing ? 'تعديل الحساب' : 'إضافة حساب جديد'),
          actions: [
            BlocConsumer<AccountBloc, AccountState>(
              listener: (context, state) {
                if (state.status == AccountStatus.success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(_isEditing 
                          ? AppConstants.accountUpdatedMessage 
                          : AppConstants.accountCreatedMessage),
                      backgroundColor: Colors.green,
                    ),
                  );
                  context.pop();
                } else if (state.status == AccountStatus.failure) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.errorMessage ?? AppConstants.genericErrorMessage),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              builder: (context, state) {
                return TextButton(
                  onPressed: state.status == AccountStatus.loading 
                      ? null 
                      : _saveAccount,
                  child: state.status == AccountStatus.loading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('حفظ'),
                );
              },
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            children: [
              // Name field
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الحساب *',
                  hintText: 'أدخل اسم الشخص أو الجهة',
                  prefixIcon: Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'اسم الحساب مطلوب';
                  }
                  if (value.trim().length > AppConstants.maxNameLength) {
                    return 'اسم الحساب طويل جداً';
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Phone field
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف',
                  hintText: 'أدخل رقم الهاتف (اختياري)',
                  prefixIcon: Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (value.length > AppConstants.maxPhoneLength) {
                      return 'رقم الهاتف طويل جداً';
                    }
                    if (!RegExp(AppConstants.phoneRegex).hasMatch(value)) {
                      return 'رقم الهاتف غير صحيح';
                    }
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Amount field
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'المبلغ *',
                  hintText: 'أدخل المبلغ',
                  prefixIcon: Icon(Icons.attach_money),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'المبلغ مطلوب';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null) {
                    return 'المبلغ غير صحيح';
                  }
                  if (amount < AppConstants.minAmount) {
                    return 'المبلغ صغير جداً';
                  }
                  if (amount > AppConstants.maxAmount) {
                    return 'المبلغ كبير جداً';
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Debt type selection
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'نوع الدين *',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: AppConstants.smallPadding),
                      Row(
                        children: [
                          Expanded(
                            child: RadioListTile<String>(
                              title: const Text('له (مدين لي)'),
                              subtitle: const Text('الشخص مدين لي'),
                              value: AppConstants.debtTypeOwedToMe,
                              groupValue: _selectedDebtType,
                              onChanged: (value) {
                                setState(() {
                                  _selectedDebtType = value!;
                                });
                              },
                            ),
                          ),
                          Expanded(
                            child: RadioListTile<String>(
                              title: const Text('عليه (أنا مدين)'),
                              subtitle: const Text('أنا مدين له'),
                              value: AppConstants.debtTypeOwedByMe,
                              groupValue: _selectedDebtType,
                              onChanged: (value) {
                                setState(() {
                                  _selectedDebtType = value!;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Category selection
              _buildCategoryDropdown(),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Currency selection
              _buildCurrencyDropdown(),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Date selection
              InkWell(
                onTap: _selectDate,
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'التاريخ *',
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ),
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Details field
              TextFormField(
                controller: _detailsController,
                decoration: const InputDecoration(
                  labelText: 'التفاصيل',
                  hintText: 'أدخل تفاصيل إضافية (اختياري)',
                  prefixIcon: Icon(Icons.notes),
                ),
                maxLines: 3,
                maxLength: AppConstants.maxDetailsLength,
                validator: (value) {
                  if (value != null && value.length > AppConstants.maxDetailsLength) {
                    return 'التفاصيل طويلة جداً';
                  }
                  return null;
                },
                textInputAction: TextInputAction.done,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryDropdown() {
    return BlocBuilder<CategoryBloc, CategoryState>(
      builder: (context, state) {
        if (state.status == CategoryStatus.loading) {
          return const LoadingWidget(message: 'تحميل الفئات...');
        }

        if (state.status == CategoryStatus.failure) {
          return Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.red),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: Text(
              'خطأ في تحميل الفئات: ${state.errorMessage}',
              style: const TextStyle(color: Colors.red),
            ),
          );
        }

        if (state.categories.isEmpty) {
          return const Text('لا توجد فئات متاحة');
        }

        // Set default category if not set
        if (_selectedCategory == null && state.defaultCategory != null) {
          _selectedCategory = state.defaultCategory;
        }

        return DropdownButtonFormField<Category>(
          value: _selectedCategory,
          decoration: const InputDecoration(
            labelText: 'الفئة *',
            prefixIcon: Icon(Icons.category),
          ),
          items: state.categories.map((category) {
            return DropdownMenuItem(
              value: category,
              child: Row(
                children: [
                  if (category.isDefault)
                    const Icon(Icons.home, size: 16),
                  if (!category.isDefault)
                    const Icon(Icons.category, size: 16),
                  const SizedBox(width: 8),
                  Text(category.name),
                ],
              ),
            );
          }).toList(),
          onChanged: (category) {
            setState(() {
              _selectedCategory = category;
            });
          },
          validator: (value) {
            if (value == null) {
              return 'الفئة مطلوبة';
            }
            return null;
          },
        );
      },
    );
  }

  Widget _buildCurrencyDropdown() {
    return BlocBuilder<CurrencyBloc, CurrencyState>(
      builder: (context, state) {
        if (state.status == CurrencyStatus.loading) {
          return const LoadingWidget(message: 'تحميل العملات...');
        }

        if (state.status == CurrencyStatus.failure) {
          return Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.red),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: Text(
              'خطأ في تحميل العملات: ${state.errorMessage}',
              style: const TextStyle(color: Colors.red),
            ),
          );
        }

        if (state.currencies.isEmpty) {
          return const Text('لا توجد عملات متاحة');
        }

        // Set default currency if not set
        if (_selectedCurrency == null && state.defaultCurrency != null) {
          _selectedCurrency = state.defaultCurrency;
        }

        return DropdownButtonFormField<Currency>(
          value: _selectedCurrency,
          decoration: const InputDecoration(
            labelText: 'العملة *',
            prefixIcon: Icon(Icons.monetization_on),
          ),
          items: state.currencies.map((currency) {
            return DropdownMenuItem(
              value: currency,
              child: Row(
                children: [
                  Text(currency.symbol),
                  const SizedBox(width: 8),
                  Text('${currency.name} (${currency.code})'),
                ],
              ),
            );
          }).toList(),
          onChanged: (currency) {
            setState(() {
              _selectedCurrency = currency;
            });
          },
          validator: (value) {
            if (value == null) {
              return 'العملة مطلوبة';
            }
            return null;
          },
        );
      },
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  void _saveAccount() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedCategory == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار الفئة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_selectedCurrency == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار العملة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final account = Account(
      id: _isEditing ? widget.account!.id : null,
      name: _nameController.text.trim(),
      phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
      amount: double.parse(_amountController.text),
      details: _detailsController.text.trim().isEmpty ? null : _detailsController.text.trim(),
      debtType: _selectedDebtType,
      date: _selectedDate.toIso8601String().split('T')[0],
      categoryId: _selectedCategory!.id!,
      currencyId: _selectedCurrency!.id!,
    );

    if (_isEditing) {
      context.read<AccountBloc>().add(UpdateAccountEvent(account));
    } else {
      context.read<AccountBloc>().add(CreateAccountEvent(account));
    }
  }
}
