import 'package:equatable/equatable.dart';

/// Account entity representing a debt account
class Account extends Equatable {
  final int? id;
  final String name;
  final String? phone;
  final double amount;
  final String? details;
  final String debtType; // 'له' or 'عليه'
  final String date;
  final int categoryId;
  final int currencyId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Account({
    this.id,
    required this.name,
    this.phone,
    required this.amount,
    this.details,
    required this.debtType,
    required this.date,
    required this.categoryId,
    required this.currencyId,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    phone,
    amount,
    details,
    debtType,
    date,
    categoryId,
    currencyId,
    createdAt,
    updatedAt,
  ];

  /// Create a copy of this Account with the given fields replaced with the new values.
  Account copyWith({
    int? id,
    String? name,
    String? phone,
    double? amount,
    String? details,
    String? debtType,
    String? date,
    int? categoryId,
    int? currencyId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Account(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      amount: amount ?? this.amount,
      details: details ?? this.details,
      debtType: debtType ?? this.debtType,
      date: date ?? this.date,
      categoryId: categoryId ?? this.categoryId,
      currencyId: currencyId ?? this.currencyId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
