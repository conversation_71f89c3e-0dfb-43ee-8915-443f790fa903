import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../entities/account.dart';
import '../../repositories/account_repository.dart';
import '../base/usecase.dart';

/// Use case for creating a new account
class CreateAccount implements UseCase<Account, Account> {
  final AccountRepository _repository;

  CreateAccount(this._repository);

  @override
  Future<Either<Failure, Account>> call(Account account) async {
    return await _repository.createAccount(account);
  }
}
