import '../../domain/entities/category.dart';

/// Category model that extends the Category entity
class CategoryModel extends Category {
  const CategoryModel({
    super.id,
    required super.name,
    super.isDefault = false,
    super.createdAt,
    super.updatedAt,
  });

  /// Create a CategoryModel from a map (e.g., from database or API)
  factory CategoryModel.fromMap(Map<String, dynamic> map) {
    return CategoryModel(
      id: map['id'],
      name: map['name'],
      isDefault: map['is_default'] == 1,
      createdAt:
          map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  /// Convert this CategoryModel to a map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'is_default': isDefault ? 1 : 0,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Create a copy of this CategoryModel with the given fields replaced with the new values.
  @override
  CategoryModel copyWith({
    int? id,
    String? name,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
