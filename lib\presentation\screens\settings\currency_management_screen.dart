import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/di/service_locator.dart';
import '../../../domain/entities/currency.dart';
import '../../bloc/currency/currency_bloc.dart';
import '../../bloc/currency/currency_event.dart';
import '../../bloc/currency/currency_state.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';

class CurrencyManagementScreen extends StatefulWidget {
  const CurrencyManagementScreen({super.key});

  @override
  State<CurrencyManagementScreen> createState() => _CurrencyManagementScreenState();
}

class _CurrencyManagementScreenState extends State<CurrencyManagementScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider<CurrencyBloc>(
      create: (context) => ServiceLocator.get<CurrencyBloc>()
        ..add(const GetCurrenciesEvent()),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('إدارة العملات'),
          actions: [
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showCurrencyDialog(),
              tooltip: 'إضافة عملة جديدة',
            ),
          ],
        ),
        body: BlocConsumer<CurrencyBloc, CurrencyState>(
          listener: (context, state) {
            if (state.status == CurrencyStatus.failure) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.errorMessage ?? AppConstants.genericErrorMessage),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          builder: (context, state) {
            if (state.status == CurrencyStatus.loading) {
              return const LoadingWidget(message: 'تحميل العملات...');
            }

            if (state.status == CurrencyStatus.failure) {
              return CustomErrorWidget(
                message: state.errorMessage ?? AppConstants.genericErrorMessage,
                onRetry: () {
                  context.read<CurrencyBloc>().add(const GetCurrenciesEvent());
                },
              );
            }

            if (state.currencies.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.monetization_on_outlined,
                      size: 64,
                      color: Theme.of(context).colorScheme.outline,
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    Text(
                      'لا توجد عملات',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    const Text('أضف عملة جديدة باستخدام الزر أعلاه'),
                    const SizedBox(height: AppConstants.largePadding),
                    ElevatedButton.icon(
                      onPressed: () => _showCurrencyDialog(),
                      icon: const Icon(Icons.add),
                      label: const Text('إضافة عملة جديدة'),
                    ),
                  ],
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: () async {
                context.read<CurrencyBloc>().add(const GetCurrenciesEvent());
              },
              child: ListView.builder(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                itemCount: state.currencies.length,
                itemBuilder: (context, index) {
                  final currency = state.currencies[index];
                  return _buildCurrencyTile(currency);
                },
              ),
            );
          },
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => _showCurrencyDialog(),
          tooltip: 'إضافة عملة جديدة',
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildCurrencyTile(Currency currency) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(AppConstants.smallPadding),
          decoration: BoxDecoration(
            color: currency.isDefault 
                ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                : Theme.of(context).colorScheme.secondary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius / 2),
          ),
          child: Text(
            currency.symbol,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: currency.isDefault 
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.secondary,
            ),
          ),
        ),
        title: Text(
          currency.name,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الرمز: ${currency.code}'),
            if (currency.isDefault)
              Text(
                'عملة افتراضية',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
          ],
        ),
        trailing: currency.isDefault 
            ? null 
            : PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _showCurrencyDialog(currency: currency);
                      break;
                    case 'delete':
                      _showDeleteConfirmation(currency);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  void _showCurrencyDialog({Currency? currency}) {
    final isEditing = currency != null;
    final nameController = TextEditingController(text: currency?.name ?? '');
    final codeController = TextEditingController(text: currency?.code ?? '');
    final symbolController = TextEditingController(text: currency?.symbol ?? '');
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(isEditing ? 'تعديل العملة' : 'إضافة عملة جديدة'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم العملة',
                  hintText: 'مثال: الريال السعودي',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'اسم العملة مطلوب';
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              TextFormField(
                controller: codeController,
                decoration: const InputDecoration(
                  labelText: 'رمز العملة',
                  hintText: 'مثال: SAR',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'رمز العملة مطلوب';
                  }
                  if (value.trim().length != 3) {
                    return 'رمز العملة يجب أن يكون 3 أحرف';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.characters,
                maxLength: 3,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              TextFormField(
                controller: symbolController,
                decoration: const InputDecoration(
                  labelText: 'رمز العملة المرئي',
                  hintText: 'مثال: ﷼',
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'رمز العملة المرئي مطلوب';
                  }
                  return null;
                },
                maxLength: 5,
                textInputAction: TextInputAction.done,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _saveCurrency(
              dialogContext,
              formKey,
              nameController.text,
              codeController.text,
              symbolController.text,
              currency,
            ),
            child: Text(isEditing ? 'تحديث' : 'إضافة'),
          ),
        ],
      ),
    );
  }

  void _saveCurrency(
    BuildContext dialogContext,
    GlobalKey<FormState> formKey,
    String name,
    String code,
    String symbol,
    Currency? existingCurrency,
  ) {
    if (!formKey.currentState!.validate()) {
      return;
    }

    final currencyToSave = Currency(
      id: existingCurrency?.id,
      name: name.trim(),
      code: code.trim().toUpperCase(),
      symbol: symbol.trim(),
      isDefault: existingCurrency?.isDefault ?? false,
      createdAt: existingCurrency?.createdAt,
      updatedAt: existingCurrency != null ? DateTime.now() : null,
    );

    if (existingCurrency != null) {
      context.read<CurrencyBloc>().add(UpdateCurrencyEvent(currencyToSave));
    } else {
      context.read<CurrencyBloc>().add(CreateCurrencyEvent(currencyToSave));
    }

    Navigator.of(dialogContext).pop();
  }

  void _showDeleteConfirmation(Currency currency) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حذف عملة "${currency.name}"؟'),
            const SizedBox(height: AppConstants.smallPadding),
            const Text(
              'ملاحظة: سيتم تحويل جميع الحسابات بهذه العملة إلى العملة الافتراضية.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.orange,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<CurrencyBloc>().add(DeleteCurrencyEvent(currency.id!));
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
