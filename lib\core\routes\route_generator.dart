import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../domain/entities/account.dart';
import '../../domain/entities/category.dart';
import '../../domain/entities/currency.dart';
import '../../presentation/screens/dashboard/dashboard_screen.dart';
import '../../presentation/screens/account_form/account_form_screen.dart';
import '../../presentation/screens/account_details/account_details_screen.dart';
import '../../presentation/screens/settings/settings_screen.dart';
import '../../presentation/screens/settings/category_management_screen.dart';
import '../../presentation/screens/settings/currency_management_screen.dart';
import '../../presentation/screens/search/search_screen.dart';
import 'app_routes.dart';

/// Application route configuration using GoRouter
class RouteGenerator {
  static final GoRouter router = GoRouter(
    initialLocation: AppRoutes.home,
    routes: [
      // Dashboard (Home) route
      GoRoute(
        path: AppRoutes.home,
        name: 'home',
        builder: (context, state) => const DashboardScreen(),
      ),
      
      // Account routes
      GoRoute(
        path: AppRoutes.accountForm,
        name: 'account-form',
        builder: (context, state) {
          final account = state.extra as Account?;
          return AccountFormScreen(account: account);
        },
      ),
      
      GoRoute(
        path: '${AppRoutes.accountDetails}/:id',
        name: 'account-details',
        builder: (context, state) {
          final accountId = int.parse(state.pathParameters['id']!);
          return AccountDetailsScreen(accountId: accountId);
        },
      ),
      
      // Settings routes
      GoRoute(
        path: AppRoutes.settings,
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
        routes: [
          GoRoute(
            path: 'categories',
            name: 'category-management',
            builder: (context, state) => const CategoryManagementScreen(),
          ),
          GoRoute(
            path: 'currencies',
            name: 'currency-management',
            builder: (context, state) => const CurrencyManagementScreen(),
          ),
        ],
      ),
      
      // Search route
      GoRoute(
        path: AppRoutes.search,
        name: 'search',
        builder: (context, state) => const SearchScreen(),
      ),
    ],
    
    // Error handling
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('خطأ'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'الصفحة غير موجودة',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              state.error?.toString() ?? 'حدث خطأ غير متوقع',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => context.go(AppRoutes.home),
              icon: const Icon(Icons.home),
              label: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );
}
