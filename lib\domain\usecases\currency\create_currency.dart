import 'package:dartz/dartz.dart';
import '../../entities/currency.dart';
import '../../repositories/currency_repository.dart';
import '../../../core/errors/failures.dart';
import '../base/usecase.dart';

/// Use case for creating a new currency
class CreateCurrency implements UseCase<Currency, Currency> {
  final CurrencyRepository repository;

  CreateCurrency(this.repository);

  @override
  Future<Either<Failure, Currency>> call(Currency currency) async {
    return await repository.createCurrency(currency);
  }
}
