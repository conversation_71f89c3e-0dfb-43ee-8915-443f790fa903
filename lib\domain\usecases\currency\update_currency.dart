import 'package:dartz/dartz.dart';
import '../../entities/currency.dart';
import '../../repositories/currency_repository.dart';
import '../../../core/errors/failures.dart';
import '../base/usecase.dart';

/// Use case for updating an existing currency
class UpdateCurrency implements UseCase<Currency, Currency> {
  final CurrencyRepository repository;

  UpdateCurrency(this.repository);

  @override
  Future<Either<Failure, Currency>> call(Currency currency) async {
    return await repository.updateCurrency(currency);
  }
}
