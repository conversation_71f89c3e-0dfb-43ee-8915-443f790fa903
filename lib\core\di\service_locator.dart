import 'package:get_it/get_it.dart';
import '../../data/datasources/local/database_helper.dart';
import '../../data/repositories/account_repository_impl.dart';
import '../../data/repositories/category_repository_impl.dart';
import '../../data/repositories/currency_repository_impl.dart';
import '../../domain/repositories/account_repository.dart';
import '../../domain/repositories/category_repository.dart';
import '../../domain/repositories/currency_repository.dart';
import '../../domain/usecases/account/create_account.dart';
import '../../domain/usecases/account/delete_account.dart';
import '../../domain/usecases/account/get_accounts.dart';
import '../../domain/usecases/account/search_accounts.dart';
import '../../domain/usecases/account/update_account.dart';
import '../../domain/usecases/category/create_category.dart';
import '../../domain/usecases/category/delete_category.dart';
import '../../domain/usecases/category/get_categories.dart';
import '../../domain/usecases/category/update_category.dart';
import '../../domain/usecases/currency/create_currency.dart';
import '../../domain/usecases/currency/delete_currency.dart';
import '../../domain/usecases/currency/get_currencies.dart';
import '../../domain/usecases/currency/update_currency.dart';
import '../../presentation/bloc/account/account_bloc.dart';
import '../../presentation/bloc/category/category_bloc.dart';
import '../../presentation/bloc/currency/currency_bloc.dart';

/// Service locator for dependency injection
class ServiceLocator {
  static final GetIt _instance = GetIt.instance;

  /// Initialize the service locator
  static Future<void> init() async {
    // Database
    _instance.registerLazySingleton<DatabaseHelper>(() => DatabaseHelper());

    // Repositories
    _instance.registerLazySingleton<AccountRepository>(
      () => AccountRepositoryImpl(_instance()),
    );
    _instance.registerLazySingleton<CategoryRepository>(
      () => CategoryRepositoryImpl(databaseHelper: _instance()),
    );
    _instance.registerLazySingleton<CurrencyRepository>(
      () => CurrencyRepositoryImpl(databaseHelper: _instance()),
    );

    // Account Use Cases
    _instance.registerLazySingleton(() => GetAccounts(_instance()));
    _instance.registerLazySingleton(() => CreateAccount(_instance()));
    _instance.registerLazySingleton(() => UpdateAccount(_instance()));
    _instance.registerLazySingleton(() => DeleteAccount(_instance()));
    _instance.registerLazySingleton(() => SearchAccounts(_instance()));

    // Category Use Cases
    _instance.registerLazySingleton(() => GetCategories(_instance()));
    _instance.registerLazySingleton(() => CreateCategory(_instance()));
    _instance.registerLazySingleton(() => UpdateCategory(_instance()));
    _instance.registerLazySingleton(() => DeleteCategory(_instance()));

    // Currency Use Cases
    _instance.registerLazySingleton(() => GetCurrencies(_instance()));
    _instance.registerLazySingleton(() => CreateCurrency(_instance()));
    _instance.registerLazySingleton(() => UpdateCurrency(_instance()));
    _instance.registerLazySingleton(() => DeleteCurrency(_instance()));

    // BLoCs
    _instance.registerFactory(
      () => AccountBloc(
        getAccounts: _instance(),
        createAccount: _instance(),
        updateAccount: _instance(),
        deleteAccount: _instance(),
        searchAccounts: _instance(),
      ),
    );
    _instance.registerFactory(
      () => CategoryBloc(
        getCategories: _instance(),
        createCategory: _instance(),
        updateCategory: _instance(),
        deleteCategory: _instance(),
      ),
    );
    _instance.registerFactory(
      () => CurrencyBloc(
        getCurrencies: _instance(),
        createCurrency: _instance(),
        updateCurrency: _instance(),
        deleteCurrency: _instance(),
      ),
    );
  }

  /// Get a registered instance
  static T get<T extends Object>() => _instance<T>();
}
