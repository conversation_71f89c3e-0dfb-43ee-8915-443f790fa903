import 'package:dartz/dartz.dart';
import '../../entities/category.dart';
import '../../repositories/category_repository.dart';
import '../../../core/errors/failures.dart';
import '../base/usecase.dart';

/// Use case for getting all categories
class GetCategories implements UseCase<List<Category>, NoParams> {
  final CategoryRepository repository;

  GetCategories(this.repository);

  @override
  Future<Either<Failure, List<Category>>> call(NoParams params) async {
    return await repository.getCategories();
  }
}
