import 'package:equatable/equatable.dart';
import '../../../domain/entities/account.dart';

/// Base class for all account events
abstract class AccountEvent extends Equatable {
  const AccountEvent();

  @override
  List<Object?> get props => [];
}

/// Event to get all accounts with optional filters
class GetAccountsEvent extends AccountEvent {
  final int? categoryId;
  final int? currencyId;

  const GetAccountsEvent({this.categoryId, this.currencyId});

  @override
  List<Object?> get props => [categoryId, currencyId];
}

/// Event to create a new account
class CreateAccountEvent extends AccountEvent {
  final Account account;

  const CreateAccountEvent({required this.account});

  @override
  List<Object> get props => [account];
}

/// Event to update an existing account
class UpdateAccountEvent extends AccountEvent {
  final Account account;

  const UpdateAccountEvent({required this.account});

  @override
  List<Object> get props => [account];
}

/// Event to delete an account
class DeleteAccountEvent extends AccountEvent {
  final int id;

  const DeleteAccountEvent({required this.id});

  @override
  List<Object> get props => [id];
}

/// Event to search accounts by query
class SearchAccountsEvent extends AccountEvent {
  final String query;

  const SearchAccountsEvent({required this.query});

  @override
  List<Object> get props => [query];
}

/// Event to filter accounts
class FilterAccountsEvent extends AccountEvent {
  final int? categoryId;
  final int? currencyId;
  final String? debtType;
  final DateTime? startDate;
  final DateTime? endDate;
  final double? minAmount;
  final double? maxAmount;

  const FilterAccountsEvent({
    this.categoryId,
    this.currencyId,
    this.debtType,
    this.startDate,
    this.endDate,
    this.minAmount,
    this.maxAmount,
  });

  @override
  List<Object?> get props => [
    categoryId,
    currencyId,
    debtType,
    startDate,
    endDate,
    minAmount,
    maxAmount,
  ];
}
