/// Base exception class for all application exceptions
class AppException implements Exception {
  final String message;
  final String? code;
  final StackTrace? stackTrace;

  AppException(this.message, {this.code, this.stackTrace});

  @override
  String toString() =>
      'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Database related exceptions
class DatabaseException extends AppException {
  DatabaseException(super.message, {super.code, super.stackTrace});

  @override
  String toString() =>
      'DatabaseException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Validation related exceptions
class ValidationException extends AppException {
  final Map<String, String> errors;

  ValidationException(
    super.message,
    this.errors, {
    super.code,
    super.stackTrace,
  });

  @override
  String toString() =>
      'ValidationException: $message${code != null ? ' (Code: $code)' : ''}\nErrors: $errors';
}

/// Network related exceptions
class NetworkException extends AppException {
  NetworkException(super.message, {super.code, super.stackTrace});

  @override
  String toString() =>
      'NetworkException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Server related exceptions
class ServerException extends AppException {
  final int? statusCode;

  ServerException(
    super.message, {
    this.statusCode,
    super.code,
    super.stackTrace,
  });

  @override
  String toString() =>
      'ServerException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}${code != null ? ' (Code: $code)' : ''}';
}

/// Cache related exceptions
class CacheException extends AppException {
  CacheException(super.message, {super.code, super.stackTrace});

  @override
  String toString() =>
      'CacheException: $message${code != null ? ' (Code: $code)' : ''}';
}
