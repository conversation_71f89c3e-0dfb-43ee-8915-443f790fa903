import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/errors/failures.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/category.dart';
import '../../../domain/usecases/category/create_category.dart';
import '../../../domain/usecases/category/delete_category.dart';
import '../../../domain/usecases/category/get_categories.dart';
import '../../../domain/usecases/category/update_category.dart';
import '../../../domain/usecases/base/usecase.dart';
import '../base/base_bloc.dart';
import 'category_event.dart';
import 'category_state.dart';

/// Category BLoC that manages category state
class CategoryBloc extends BaseBloc<CategoryEvent, CategoryState> {
  final CreateCategory _createCategory;
  final GetCategories _getCategories;
  final UpdateCategory _updateCategory;
  final DeleteCategory _deleteCategory;

  // Retry mechanism
  static const int _maxRetries = 3;
  int _getCategoriesRetryCount = 0;

  CategoryBloc({
    required CreateCategory createCategory,
    required GetCategories getCategories,
    required UpdateCategory updateCategory,
    required DeleteCategory deleteCategory,
  })  : _createCategory = createCategory,
        _getCategories = getCategories,
        _updateCategory = updateCategory,
        _deleteCategory = deleteCategory,
        super(const CategoryState()) {
    on<GetCategoriesEvent>(_onGetCategories);
    on<CreateCategoryEvent>(_onCreateCategory);
    on<UpdateCategoryEvent>(_onUpdateCategory);
    on<DeleteCategoryEvent>(_onDeleteCategory);
    on<SearchCategoriesEvent>(_onSearchCategories);
    on<ClearCategorySearchEvent>(_onClearCategorySearch);
    on<SelectCategoryEvent>(_onSelectCategory);
  }

  Future<void> _onGetCategories(
    GetCategoriesEvent event,
    Emitter<CategoryState> emit,
  ) async {
    if (_getCategoriesRetryCount >= _maxRetries) {
      emit(
        state.copyWith(
          status: CategoryStatus.failure,
          errorMessage: 'فشل في تحميل الفئات بعد عدة محاولات',
        ),
      );
      return;
    }

    emit(state.copyWith(status: CategoryStatus.loading));

    try {
      final result = await _getCategories(NoParams());

      result.fold(
        (failure) {
          _getCategoriesRetryCount++;
          Logger.error('Failed to get categories', failure);
          emit(
            state.copyWith(
              status: CategoryStatus.failure,
              errorMessage: _mapFailureToMessage(failure),
            ),
          );
        },
        (categories) {
          emit(
            state.copyWith(
              status: CategoryStatus.success,
              categories: categories,
              filteredCategories: categories,
              clearErrorMessage: true,
            ),
          );
          _getCategoriesRetryCount = 0;
        },
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in getCategories', e, stackTrace);
      emit(
        state.copyWith(
          status: CategoryStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onCreateCategory(
    CreateCategoryEvent event,
    Emitter<CategoryState> emit,
  ) async {
    emit(state.copyWith(status: CategoryStatus.loading));

    try {
      final result = await _createCategory(event.category);

      result.fold(
        (failure) => emit(
          state.copyWith(
            status: CategoryStatus.failure,
            errorMessage: _mapFailureToMessage(failure),
          ),
        ),
        (category) {
          final updatedCategories = List<Category>.from(state.categories)
            ..add(category);
          emit(
            state.copyWith(
              status: CategoryStatus.success,
              categories: updatedCategories,
              filteredCategories: _applyFilters(
                updatedCategories,
                state.searchQuery,
              ),
              clearErrorMessage: true,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in createCategory', e, stackTrace);
      emit(
        state.copyWith(
          status: CategoryStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onUpdateCategory(
    UpdateCategoryEvent event,
    Emitter<CategoryState> emit,
  ) async {
    emit(state.copyWith(status: CategoryStatus.loading));

    try {
      final result = await _updateCategory(event.category);

      result.fold(
        (failure) => emit(
          state.copyWith(
            status: CategoryStatus.failure,
            errorMessage: _mapFailureToMessage(failure),
          ),
        ),
        (updatedCategory) {
          final updatedCategories = state.categories.map((category) {
            return category.id == updatedCategory.id
                ? updatedCategory
                : category;
          }).toList();

          emit(
            state.copyWith(
              status: CategoryStatus.success,
              categories: updatedCategories,
              filteredCategories: _applyFilters(
                updatedCategories,
                state.searchQuery,
              ),
              clearErrorMessage: true,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in updateCategory', e, stackTrace);
      emit(
        state.copyWith(
          status: CategoryStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onDeleteCategory(
    DeleteCategoryEvent event,
    Emitter<CategoryState> emit,
  ) async {
    emit(state.copyWith(status: CategoryStatus.loading));

    try {
      final result = await _deleteCategory(
        DeleteCategoryParams(categoryId: event.categoryId),
      );

      result.fold(
        (failure) => emit(
          state.copyWith(
            status: CategoryStatus.failure,
            errorMessage: _mapFailureToMessage(failure),
          ),
        ),
        (_) {
          final updatedCategories = state.categories
              .where((category) => category.id != event.categoryId)
              .toList();

          emit(
            state.copyWith(
              status: CategoryStatus.success,
              categories: updatedCategories,
              filteredCategories: _applyFilters(
                updatedCategories,
                state.searchQuery,
              ),
              clearErrorMessage: true,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in deleteCategory', e, stackTrace);
      emit(
        state.copyWith(
          status: CategoryStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  void _onSearchCategories(
    SearchCategoriesEvent event,
    Emitter<CategoryState> emit,
  ) {
    final filteredCategories = _applyFilters(state.categories, event.query);
    emit(
      state.copyWith(
        searchQuery: event.query,
        filteredCategories: filteredCategories,
        isSearching: event.query.isNotEmpty,
      ),
    );
  }

  void _onClearCategorySearch(
    ClearCategorySearchEvent event,
    Emitter<CategoryState> emit,
  ) {
    emit(
      state.copyWith(
        searchQuery: '',
        filteredCategories: state.categories,
        isSearching: false,
      ),
    );
  }

  void _onSelectCategory(
    SelectCategoryEvent event,
    Emitter<CategoryState> emit,
  ) {
    emit(state.copyWith(selectedCategory: event.category));
  }

  /// Apply search filters to categories
  List<Category> _applyFilters(List<Category> categories, String query) {
    if (query.isEmpty) return categories;

    return categories
        .where((category) =>
            category.name.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case DatabaseFailure:
        return 'خطأ في قاعدة البيانات: ${failure.message}';
      case ValidationFailure:
        return 'خطأ في التحقق من البيانات: ${failure.message}';
      case NetworkFailure:
        return 'خطأ في الشبكة: ${failure.message}';
      default:
        return failure.message;
    }
  }
}
