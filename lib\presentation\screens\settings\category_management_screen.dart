import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/di/service_locator.dart';
import '../../../domain/entities/category.dart';
import '../../bloc/category/category_bloc.dart';
import '../../bloc/category/category_event.dart';
import '../../bloc/category/category_state.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';

class CategoryManagementScreen extends StatefulWidget {
  const CategoryManagementScreen({super.key});

  @override
  State<CategoryManagementScreen> createState() => _CategoryManagementScreenState();
}

class _CategoryManagementScreenState extends State<CategoryManagementScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider<CategoryBloc>(
      create: (context) => ServiceLocator.get<CategoryBloc>()
        ..add(const GetCategoriesEvent()),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('إدارة الفئات'),
          actions: [
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showCategoryDialog(),
              tooltip: 'إضافة فئة جديدة',
            ),
          ],
        ),
        body: BlocConsumer<CategoryBloc, CategoryState>(
          listener: (context, state) {
            if (state.status == CategoryStatus.success && state.categories.isNotEmpty) {
              // Show success message when category is created/updated/deleted
              final message = _getSuccessMessage(state);
              if (message != null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(message),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            } else if (state.status == CategoryStatus.failure) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.errorMessage ?? AppConstants.genericErrorMessage),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          builder: (context, state) {
            if (state.status == CategoryStatus.loading) {
              return const LoadingWidget(message: 'تحميل الفئات...');
            }

            if (state.status == CategoryStatus.failure) {
              return CustomErrorWidget(
                message: state.errorMessage ?? AppConstants.genericErrorMessage,
                onRetry: () {
                  context.read<CategoryBloc>().add(const GetCategoriesEvent());
                },
              );
            }

            if (state.categories.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.category_outlined,
                      size: 64,
                      color: Theme.of(context).colorScheme.outline,
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    Text(
                      'لا توجد فئات',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    const Text('أضف فئة جديدة باستخدام الزر أعلاه'),
                    const SizedBox(height: AppConstants.largePadding),
                    ElevatedButton.icon(
                      onPressed: () => _showCategoryDialog(),
                      icon: const Icon(Icons.add),
                      label: const Text('إضافة فئة جديدة'),
                    ),
                  ],
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: () async {
                context.read<CategoryBloc>().add(const GetCategoriesEvent());
              },
              child: ListView.builder(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                itemCount: state.categories.length,
                itemBuilder: (context, index) {
                  final category = state.categories[index];
                  return _buildCategoryTile(category);
                },
              ),
            );
          },
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => _showCategoryDialog(),
          tooltip: 'إضافة فئة جديدة',
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  Widget _buildCategoryTile(Category category) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(AppConstants.smallPadding),
          decoration: BoxDecoration(
            color: category.isDefault 
                ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                : Theme.of(context).colorScheme.secondary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius / 2),
          ),
          child: Icon(
            category.isDefault ? Icons.home : Icons.category,
            color: category.isDefault 
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.secondary,
          ),
        ),
        title: Text(
          category.name,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        subtitle: Text(
          category.isDefault ? 'فئة افتراضية' : 'فئة مخصصة',
          style: TextStyle(
            color: category.isDefault 
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline,
          ),
        ),
        trailing: category.isDefault 
            ? null 
            : PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _showCategoryDialog(category: category);
                      break;
                    case 'delete':
                      _showDeleteConfirmation(category);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  void _showCategoryDialog({Category? category}) {
    final isEditing = category != null;
    final nameController = TextEditingController(text: category?.name ?? '');
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(isEditing ? 'تعديل الفئة' : 'إضافة فئة جديدة'),
        content: Form(
          key: formKey,
          child: TextFormField(
            controller: nameController,
            decoration: const InputDecoration(
              labelText: 'اسم الفئة',
              hintText: 'أدخل اسم الفئة',
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'اسم الفئة مطلوب';
              }
              if (value.trim().length > AppConstants.maxNameLength) {
                return 'اسم الفئة طويل جداً';
              }
              return null;
            },
            autofocus: true,
            textInputAction: TextInputAction.done,
            onFieldSubmitted: (_) => _saveCategory(
              dialogContext,
              formKey,
              nameController.text,
              category,
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _saveCategory(
              dialogContext,
              formKey,
              nameController.text,
              category,
            ),
            child: Text(isEditing ? 'تحديث' : 'إضافة'),
          ),
        ],
      ),
    );
  }

  void _saveCategory(
    BuildContext dialogContext,
    GlobalKey<FormState> formKey,
    String name,
    Category? existingCategory,
  ) {
    if (!formKey.currentState!.validate()) {
      return;
    }

    final categoryToSave = Category(
      id: existingCategory?.id,
      name: name.trim(),
      isDefault: existingCategory?.isDefault ?? false,
      createdAt: existingCategory?.createdAt,
      updatedAt: existingCategory != null ? DateTime.now() : null,
    );

    if (existingCategory != null) {
      context.read<CategoryBloc>().add(UpdateCategoryEvent(categoryToSave));
    } else {
      context.read<CategoryBloc>().add(CreateCategoryEvent(categoryToSave));
    }

    Navigator.of(dialogContext).pop();
  }

  void _showDeleteConfirmation(Category category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حذف فئة "${category.name}"؟'),
            const SizedBox(height: AppConstants.smallPadding),
            const Text(
              'ملاحظة: سيتم نقل جميع الحسابات في هذه الفئة إلى الفئة العامة.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.orange,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<CategoryBloc>().add(DeleteCategoryEvent(category.id!));
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  String? _getSuccessMessage(CategoryState state) {
    // This is a simple way to detect success operations
    // In a real app, you might want to track the last operation type
    return null; // For now, we'll rely on the listener for error messages only
  }
}
