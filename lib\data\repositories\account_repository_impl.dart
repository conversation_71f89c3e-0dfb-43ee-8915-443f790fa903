import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/account.dart';
import '../../domain/repositories/account_repository.dart';
import '../datasources/local/database_helper.dart';
import '../models/account_model.dart';

/// Implementation of the AccountRepository interface
class AccountRepositoryImpl implements AccountRepository {
  final DatabaseHelper _databaseHelper;
  static const int _maxRetries = 2;

  AccountRepositoryImpl(this._databaseHelper);

  @override
  Future<Either<Failure, List<Account>>> getAccounts({
    int? categoryId,
    int? currencyId,
  }) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;

      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (categoryId != null) {
        whereClause += 'category_id = ?';
        whereArgs.add(categoryId);
      }

      if (currencyId != null) {
        if (whereClause.isNotEmpty) {
          whereClause += ' AND ';
        }
        whereClause += 'currency_id = ?';
        whereArgs.add(currencyId);
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'accounts',
        where: whereClause.isNotEmpty ? whereClause : null,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'date DESC',
      );

      final accounts = maps.map((map) => AccountModel.fromMap(map)).toList();
      return Right(accounts);
    }, 'Failed to get accounts');
  }

  @override
  Future<Either<Failure, Account>> getAccountById(int id) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'accounts',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isEmpty) {
        return Left(DatabaseFailure('لم يتم العثور على الحساب'));
      }

      final account = AccountModel.fromMap(maps.first);
      return Right(account);
    }, 'Failed to get account by ID');
  }

  @override
  Future<Either<Failure, Account>> createAccount(Account account) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;

      // Convert Account entity to AccountModel
      final accountModel =
          account is AccountModel
              ? account
              : AccountModel(
                name: account.name,
                phone: account.phone,
                amount: account.amount,
                details: account.details,
                debtType: account.debtType,
                date: account.date,
                categoryId: account.categoryId,
                currencyId: account.currencyId,
              );

      final id = await db.insert('accounts', accountModel.toMap());

      return Right(accountModel.copyWith(id: id));
    }, 'Failed to create account');
  }

  @override
  Future<Either<Failure, Account>> updateAccount(Account account) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;

      if (account.id == null) {
        return Left(
          ValidationFailure('معرف الحساب مطلوب للتحديث', {
            'id': 'معرف الحساب مطلوب للتحديث',
          }),
        );
      }

      // Convert Account entity to AccountModel
      final accountModel =
          account is AccountModel
              ? account
              : AccountModel(
                id: account.id,
                name: account.name,
                phone: account.phone,
                amount: account.amount,
                details: account.details,
                debtType: account.debtType,
                date: account.date,
                categoryId: account.categoryId,
                currencyId: account.currencyId,
              );

      final count = await db.update(
        'accounts',
        accountModel.toMap(),
        where: 'id = ?',
        whereArgs: [account.id],
      );

      if (count == 0) {
        return Left(
          DatabaseFailure('لم يتم العثور على الحساب أو لم يتم تحديثه'),
        );
      }

      return Right(accountModel);
    }, 'Failed to update account');
  }

  @override
  Future<Either<Failure, void>> deleteAccount(int id) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;

      final count = await db.delete(
        'accounts',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (count == 0) {
        return Left(DatabaseFailure('لم يتم العثور على الحساب أو لم يتم حذفه'));
      }

      return const Right(null);
    }, 'Failed to delete account');
  }

  @override
  Future<Either<Failure, List<Account>>> searchAccounts(String query) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        'accounts',
        where: 'name LIKE ?',
        whereArgs: ['%$query%'],
        orderBy: 'date DESC',
      );

      final accounts = maps.map((map) => AccountModel.fromMap(map)).toList();
      return Right(accounts);
    }, 'Failed to search accounts');
  }

  /// Execute a database operation with retry logic
  Future<Either<Failure, T>> _executeWithRetry<T>(
    Future<Either<Failure, T>> Function() operation,
    String errorMessage,
  ) async {
    int retryCount = 0;

    while (retryCount <= _maxRetries) {
      try {
        return await operation();
      } catch (e, stackTrace) {
        retryCount++;

        if (retryCount <= _maxRetries) {
          Logger.warning(
            '$errorMessage, retrying (attempt $retryCount/$_maxRetries)...',
            e,
          );
          await Future.delayed(Duration(milliseconds: 500 * retryCount));
        } else {
          Logger.error(errorMessage, e, stackTrace);
          return Left(
            DatabaseFailure('خطأ في قاعدة البيانات: ${e.toString()}'),
          );
        }
      }
    }

    // This should never be reached
    return Left(DatabaseFailure('خطأ غير متوقع في قاعدة البيانات'));
  }
}
