import 'package:dartz/dartz.dart';
import '../../entities/category.dart';
import '../../repositories/category_repository.dart';
import '../../../core/errors/failures.dart';
import '../base/usecase.dart';

/// Use case for creating a new category
class CreateCategory implements UseCase<Category, Category> {
  final CategoryRepository repository;

  CreateCategory(this.repository);

  @override
  Future<Either<Failure, Category>> call(Category category) async {
    return await repository.createCategory(category);
  }
}
