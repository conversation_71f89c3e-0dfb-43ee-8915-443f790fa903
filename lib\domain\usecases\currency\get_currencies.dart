import 'package:dartz/dartz.dart';
import '../../entities/currency.dart';
import '../../repositories/currency_repository.dart';
import '../../../core/errors/failures.dart';
import '../base/usecase.dart';

/// Use case for getting all currencies
class GetCurrencies implements UseCase<List<Currency>, NoParams> {
  final CurrencyRepository repository;

  GetCurrencies(this.repository);

  @override
  Future<Either<Failure, List<Currency>>> call(NoParams params) async {
    return await repository.getCurrencies();
  }
}
