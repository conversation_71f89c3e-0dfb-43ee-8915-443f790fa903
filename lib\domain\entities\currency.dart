import 'package:equatable/equatable.dart';

/// Currency entity representing a monetary currency
class Currency extends Equatable {
  final int? id;
  final String name;
  final String code;
  final String symbol;
  final bool isDefault;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Currency({
    this.id,
    required this.name,
    required this.code,
    required this.symbol,
    this.isDefault = false,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    code,
    symbol,
    isDefault,
    createdAt,
    updatedAt,
  ];

  /// Create a copy of this Currency with the given fields replaced with the new values.
  Currency copyWith({
    int? id,
    String? name,
    String? code,
    String? symbol,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Currency(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      symbol: symbol ?? this.symbol,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
