import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/theme/app_theme.dart';
import '../../../domain/entities/account.dart';
import '../../bloc/account/account_bloc.dart';
import '../../bloc/account/account_event.dart';
import '../../bloc/account/account_state.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';

class AccountDetailsScreen extends StatefulWidget {
  final int accountId;

  const AccountDetailsScreen({
    super.key,
    required this.accountId,
  });

  @override
  State<AccountDetailsScreen> createState() => _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends State<AccountDetailsScreen> {
  Account? _account;

  @override
  void initState() {
    super.initState();
    _loadAccount();
  }

  void _loadAccount() {
    final accountBloc = context.read<AccountBloc>();
    final accounts = accountBloc.state.accounts;
    
    try {
      _account = accounts.firstWhere((account) => account.id == widget.accountId);
    } catch (e) {
      _account = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_account?.name ?? 'تفاصيل الحساب'),
        actions: [
          if (_account != null) ...[
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _editAccount(),
              tooltip: 'تعديل',
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteConfirmation(),
              tooltip: 'حذف',
            ),
          ],
        ],
      ),
      body: BlocListener<AccountBloc, AccountState>(
        listener: (context, state) {
          if (state.status == AccountStatus.success) {
            // Reload account data after updates
            _loadAccount();
            setState(() {});
          }
        },
        child: _account == null ? _buildNotFound() : _buildAccountDetails(),
      ),
    );
  }

  Widget _buildNotFound() {
    return const CustomErrorWidget(
      message: 'لم يتم العثور على الحساب',
      icon: Icons.account_balance_wallet_outlined,
    );
  }

  Widget _buildAccountDetails() {
    final account = _account!;
    final isOwedToMe = account.debtType == AppConstants.debtTypeOwedToMe;
    final debtColor = isOwedToMe ? AppTheme.owedToMeColor : AppTheme.owedByMeColor;
    final debtIcon = isOwedToMe ? Icons.arrow_upward : Icons.arrow_downward;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main info card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.largePadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name
                  Row(
                    children: [
                      Icon(
                        Icons.person,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: AppConstants.smallPadding),
                      Expanded(
                        child: Text(
                          account.name,
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppConstants.largePadding),
                  
                  // Amount and debt type
                  Container(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    decoration: BoxDecoration(
                      color: debtColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                      border: Border.all(color: debtColor.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(AppConstants.smallPadding),
                          decoration: BoxDecoration(
                            color: debtColor,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            debtIcon,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: AppConstants.defaultPadding),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                account.debtType,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      color: debtColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                _formatAmount(account.amount),
                                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                      color: debtColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          // Details card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'التفاصيل',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  
                  // Phone
                  if (account.phone != null && account.phone!.isNotEmpty)
                    _buildDetailRow(
                      icon: Icons.phone,
                      label: 'رقم الهاتف',
                      value: account.phone!,
                      onTap: () => _callPhone(account.phone!),
                    ),
                  
                  // Date
                  _buildDetailRow(
                    icon: Icons.calendar_today,
                    label: 'التاريخ',
                    value: _formatDate(account.date),
                  ),
                  
                  // Category (placeholder - will be implemented later)
                  _buildDetailRow(
                    icon: Icons.category,
                    label: 'الفئة',
                    value: 'فئة ${account.categoryId}', // TODO: Get actual category name
                  ),
                  
                  // Currency (placeholder - will be implemented later)
                  _buildDetailRow(
                    icon: Icons.monetization_on,
                    label: 'العملة',
                    value: 'عملة ${account.currencyId}', // TODO: Get actual currency name
                  ),
                  
                  // Details/Notes
                  if (account.details != null && account.details!.isNotEmpty) ...[
                    const SizedBox(height: AppConstants.defaultPadding),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.notes,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        const SizedBox(width: AppConstants.defaultPadding),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'ملاحظات',
                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                      color: Theme.of(context).colorScheme.outline,
                                    ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                account.details!,
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _editAccount,
                  icon: const Icon(Icons.edit),
                  label: const Text('تعديل'),
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _showDeleteConfirmation,
                  icon: const Icon(Icons.delete, color: Colors.red),
                  label: const Text('حذف', style: TextStyle(color: Colors.red)),
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.red),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
    VoidCallback? onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius / 2),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: AppConstants.smallPadding / 2),
          child: Row(
            children: [
              Icon(
                icon,
                color: Theme.of(context).colorScheme.outline,
                size: 20,
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            color: Theme.of(context).colorScheme.outline,
                          ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      value,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ],
                ),
              ),
              if (onTap != null)
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Theme.of(context).colorScheme.outline,
                ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatAmount(double amount) {
    final formatter = NumberFormat('#,##0.00', 'ar');
    return formatter.format(amount);
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final formatter = DateFormat('EEEE، dd MMMM yyyy', 'ar');
      return formatter.format(date);
    } catch (e) {
      return dateString;
    }
  }

  void _editAccount() {
    context.push(AppRoutes.accountForm, extra: _account);
  }

  void _callPhone(String phone) {
    // TODO: Implement phone calling functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('الاتصال بـ $phone')),
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف حساب "${_account!.name}"؟\n\nلا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AccountBloc>().add(DeleteAccountEvent(id: _account!.id!));
              context.pop(); // Go back to previous screen
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
