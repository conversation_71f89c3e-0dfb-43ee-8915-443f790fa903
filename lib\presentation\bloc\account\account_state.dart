import 'package:equatable/equatable.dart';
import '../../../domain/entities/account.dart';

/// Enum for different account status states
enum AccountStatus { initial, loading, success, failure }

/// Account filter class for filtering accounts
class AccountFilter extends Equatable {
  final int? categoryId;
  final int? currencyId;
  final String? debtType;
  final DateTime? startDate;
  final DateTime? endDate;
  final double? minAmount;
  final double? maxAmount;

  const AccountFilter({
    this.categoryId,
    this.currencyId,
    this.debtType,
    this.startDate,
    this.endDate,
    this.minAmount,
    this.maxAmount,
  });

  @override
  List<Object?> get props => [
    categoryId,
    currencyId,
    debtType,
    startDate,
    endDate,
    minAmount,
    maxAmount,
  ];

  /// Create a copy of this AccountFilter with the given fields replaced with the new values.
  AccountFilter copyWith({
    int? categoryId,
    int? currencyId,
    String? debtType,
    DateTime? startDate,
    DateTime? endDate,
    double? minAmount,
    double? maxAmount,
    bool clearCategoryId = false,
    bool clearCurrencyId = false,
    bool clearDebtType = false,
    bool clearStartDate = false,
    bool clearEndDate = false,
    bool clearMinAmount = false,
    bool clearMaxAmount = false,
  }) {
    return AccountFilter(
      categoryId: clearCategoryId ? null : (categoryId ?? this.categoryId),
      currencyId: clearCurrencyId ? null : (currencyId ?? this.currencyId),
      debtType: clearDebtType ? null : (debtType ?? this.debtType),
      startDate: clearStartDate ? null : (startDate ?? this.startDate),
      endDate: clearEndDate ? null : (endDate ?? this.endDate),
      minAmount: clearMinAmount ? null : (minAmount ?? this.minAmount),
      maxAmount: clearMaxAmount ? null : (maxAmount ?? this.maxAmount),
    );
  }
}

/// State class for the account BLoC
class AccountState extends Equatable {
  final AccountStatus status;
  final List<Account> accounts;
  final List<Account> filteredAccounts;
  final String? errorMessage;
  final AccountFilter? currentFilter;
  final String? searchQuery;

  const AccountState({
    this.status = AccountStatus.initial,
    this.accounts = const [],
    this.filteredAccounts = const [],
    this.errorMessage,
    this.currentFilter,
    this.searchQuery,
  });

  /// Initial state constructor
  const AccountState.initial()
    : status = AccountStatus.initial,
      accounts = const [],
      filteredAccounts = const [],
      errorMessage = null,
      currentFilter = null,
      searchQuery = null;

  @override
  List<Object?> get props => [
    status,
    accounts,
    filteredAccounts,
    errorMessage,
    currentFilter,
    searchQuery,
  ];

  /// Create a copy of this AccountState with the given fields replaced with the new values.
  AccountState copyWith({
    AccountStatus? status,
    List<Account>? accounts,
    List<Account>? filteredAccounts,
    String? errorMessage,
    AccountFilter? currentFilter,
    String? searchQuery,
    bool clearErrorMessage = false,
    bool clearCurrentFilter = false,
    bool clearSearchQuery = false,
  }) {
    return AccountState(
      status: status ?? this.status,
      accounts: accounts ?? this.accounts,
      filteredAccounts: filteredAccounts ?? (accounts ?? this.filteredAccounts),
      errorMessage:
          clearErrorMessage ? null : (errorMessage ?? this.errorMessage),
      currentFilter:
          clearCurrentFilter ? null : (currentFilter ?? this.currentFilter),
      searchQuery: clearSearchQuery ? null : (searchQuery ?? this.searchQuery),
    );
  }
}
