import 'package:equatable/equatable.dart';
import '../../../domain/entities/category.dart';

/// Category status enumeration
enum CategoryStatus {
  initial,
  loading,
  success,
  failure,
}

/// Category state class
class CategoryState extends Equatable {
  final CategoryStatus status;
  final List<Category> categories;
  final List<Category> filteredCategories;
  final Category? selectedCategory;
  final String? errorMessage;
  final String searchQuery;
  final bool isSearching;

  const CategoryState({
    this.status = CategoryStatus.initial,
    this.categories = const [],
    this.filteredCategories = const [],
    this.selectedCategory,
    this.errorMessage,
    this.searchQuery = '',
    this.isSearching = false,
  });

  @override
  List<Object?> get props => [
        status,
        categories,
        filteredCategories,
        selectedCategory,
        errorMessage,
        searchQuery,
        isSearching,
      ];

  /// Create a copy of this state with the given fields replaced with the new values.
  CategoryState copyWith({
    CategoryStatus? status,
    List<Category>? categories,
    List<Category>? filteredCategories,
    Category? selectedCategory,
    String? errorMessage,
    String? searchQuery,
    bool? isSearching,
    bool clearErrorMessage = false,
    bool clearSelectedCategory = false,
  }) {
    return CategoryState(
      status: status ?? this.status,
      categories: categories ?? this.categories,
      filteredCategories: filteredCategories ?? this.filteredCategories,
      selectedCategory: clearSelectedCategory ? null : (selectedCategory ?? this.selectedCategory),
      errorMessage: clearErrorMessage ? null : (errorMessage ?? this.errorMessage),
      searchQuery: searchQuery ?? this.searchQuery,
      isSearching: isSearching ?? this.isSearching,
    );
  }

  /// Check if the state is loading
  bool get isLoading => status == CategoryStatus.loading;

  /// Check if the state has error
  bool get hasError => status == CategoryStatus.failure && errorMessage != null;

  /// Check if the state is successful
  bool get isSuccess => status == CategoryStatus.success;

  /// Check if categories are empty
  bool get isEmpty => categories.isEmpty;

  /// Get the default category
  Category? get defaultCategory {
    try {
      return categories.firstWhere((category) => category.isDefault);
    } catch (e) {
      return null;
    }
  }

  /// Get non-default categories
  List<Category> get customCategories {
    return categories.where((category) => !category.isDefault).toList();
  }
}
