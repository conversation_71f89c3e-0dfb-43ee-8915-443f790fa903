/// Application constants
class AppConstants {
  // App Info
  static const String appName = 'إدارة الديون والحسابات';
  static const String appVersion = '1.0.0';
  
  // Database
  static const String databaseName = 'debt_manager.db';
  static const int databaseVersion = 1;
  
  // Default values
  static const String defaultCategoryName = 'عام';
  static const String defaultCurrencyCode = 'SAR';
  static const String defaultCurrencyName = 'ريال سعودي';
  static const String defaultCurrencySymbol = '﷼';
  
  // Debt types
  static const String debtTypeOwedToMe = 'له'; // They owe me
  static const String debtTypeOwedByMe = 'عليه'; // I owe them
  
  // Validation
  static const int maxNameLength = 100;
  static const int maxDetailsLength = 500;
  static const int maxPhoneLength = 20;
  static const double maxAmount = 999999999.99;
  static const double minAmount = 0.01;
  
  // UI
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 2.0;
  
  // Animation durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Date formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String arabicDateFormat = 'dd MMMM yyyy';
  
  // Regex patterns
  static const String phoneRegex = r'^[\+]?[0-9]{10,15}$';
  static const String amountRegex = r'^\d+(\.\d{1,2})?$';
  
  // Error messages
  static const String genericErrorMessage = 'حدث خطأ غير متوقع';
  static const String networkErrorMessage = 'تحقق من اتصال الإنترنت';
  static const String databaseErrorMessage = 'خطأ في قاعدة البيانات';
  static const String validationErrorMessage = 'يرجى التحقق من البيانات المدخلة';
  
  // Success messages
  static const String accountCreatedMessage = 'تم إنشاء الحساب بنجاح';
  static const String accountUpdatedMessage = 'تم تحديث الحساب بنجاح';
  static const String accountDeletedMessage = 'تم حذف الحساب بنجاح';
  static const String categoryCreatedMessage = 'تم إنشاء الفئة بنجاح';
  static const String categoryUpdatedMessage = 'تم تحديث الفئة بنجاح';
  static const String categoryDeletedMessage = 'تم حذف الفئة بنجاح';
  static const String currencyCreatedMessage = 'تم إنشاء العملة بنجاح';
  static const String currencyUpdatedMessage = 'تم تحديث العملة بنجاح';
  static const String currencyDeletedMessage = 'تم حذف العملة بنجاح';
}
