import 'package:dartz/dartz.dart';
import '../../repositories/currency_repository.dart';
import '../../../core/errors/failures.dart';
import '../base/usecase.dart';

/// Parameters for deleting a currency
class DeleteCurrencyParams {
  final int currencyId;

  DeleteCurrencyParams({required this.currencyId});
}

/// Use case for deleting a currency
class DeleteCurrency implements UseCase<void, DeleteCurrencyParams> {
  final CurrencyRepository repository;

  DeleteCurrency(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteCurrencyParams params) async {
    return await repository.deleteCurrency(params.currencyId);
  }
}
