import 'package:dartz/dartz.dart';
import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/category.dart';
import '../../domain/repositories/category_repository.dart';
import '../datasources/local/database_helper.dart';
import '../models/category_model.dart';

/// Implementation of CategoryRepository
class CategoryRepositoryImpl implements CategoryRepository {
  final DatabaseHelper _databaseHelper;

  CategoryRepositoryImpl({
    required DatabaseHelper databaseHelper,
  }) : _databaseHelper = databaseHelper;

  @override
  Future<Either<Failure, List<Category>>> getCategories() async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;
      final maps = await db.query(
        'categories',
        orderBy: 'is_default DESC, name ASC',
      );

      final categories = maps.map((map) => CategoryModel.fromMap(map)).toList();
      Logger.debug('Retrieved ${categories.length} categories');
      return Right(categories);
    }, 'Failed to get categories');
  }

  @override
  Future<Either<Failure, Category>> getCategoryById(int id) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;
      final maps = await db.query(
        'categories',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isEmpty) {
        throw DatabaseException('Category with id $id not found');
      }

      final category = CategoryModel.fromMap(maps.first);
      Logger.debug('Retrieved category: ${category.name}');
      return Right(category);
    }, 'Failed to get category by id');
  }

  @override
  Future<Either<Failure, Category>> createCategory(Category category) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;

      // Convert Category entity to CategoryModel
      final categoryModel = category is CategoryModel
          ? category
          : CategoryModel(
              name: category.name,
              isDefault: category.isDefault,
            );

      // Check if category name already exists
      final existingCategories = await db.query(
        'categories',
        where: 'LOWER(name) = LOWER(?)',
        whereArgs: [categoryModel.name],
      );

      if (existingCategories.isNotEmpty) {
        throw ValidationException(
          'Category name already exists',
          {'name': 'اسم الفئة موجود بالفعل'},
        );
      }

      final id = await db.insert('categories', categoryModel.toMap());
      Logger.debug('Created category with id: $id');

      return Right(categoryModel.copyWith(id: id));
    }, 'Failed to create category');
  }

  @override
  Future<Either<Failure, Category>> updateCategory(Category category) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;

      if (category.id == null) {
        throw ValidationException(
          'Category ID is required for update',
          {'id': 'معرف الفئة مطلوب للتحديث'},
        );
      }

      // Convert Category entity to CategoryModel
      final categoryModel = category is CategoryModel
          ? category
          : CategoryModel(
              id: category.id,
              name: category.name,
              isDefault: category.isDefault,
              createdAt: category.createdAt,
              updatedAt: DateTime.now(),
            );

      // Check if category name already exists (excluding current category)
      final existingCategories = await db.query(
        'categories',
        where: 'LOWER(name) = LOWER(?) AND id != ?',
        whereArgs: [categoryModel.name, categoryModel.id],
      );

      if (existingCategories.isNotEmpty) {
        throw ValidationException(
          'Category name already exists',
          {'name': 'اسم الفئة موجود بالفعل'},
        );
      }

      final rowsAffected = await db.update(
        'categories',
        categoryModel.toMap(),
        where: 'id = ?',
        whereArgs: [categoryModel.id],
      );

      if (rowsAffected == 0) {
        throw DatabaseException('Category not found or could not be updated');
      }

      Logger.debug('Updated category with id: ${categoryModel.id}');
      return Right(categoryModel);
    }, 'Failed to update category');
  }

  @override
  Future<Either<Failure, void>> deleteCategory(int id) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;

      // Check if category exists and is not default
      final categoryMaps = await db.query(
        'categories',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (categoryMaps.isEmpty) {
        throw DatabaseException('Category not found');
      }

      final category = CategoryModel.fromMap(categoryMaps.first);
      if (category.isDefault) {
        throw ValidationException(
          'Cannot delete default category',
          {'category': 'لا يمكن حذف الفئة الافتراضية'},
        );
      }

      // Get default category to move accounts to
      final defaultCategoryMaps = await db.query(
        'categories',
        where: 'is_default = 1',
        limit: 1,
      );

      if (defaultCategoryMaps.isEmpty) {
        throw DatabaseException('Default category not found');
      }

      final defaultCategory = CategoryModel.fromMap(defaultCategoryMaps.first);

      // Start transaction
      await db.transaction((txn) async {
        // Move all accounts from this category to default category
        await txn.update(
          'accounts',
          {'category_id': defaultCategory.id},
          where: 'category_id = ?',
          whereArgs: [id],
        );

        // Delete the category
        await txn.delete(
          'categories',
          where: 'id = ?',
          whereArgs: [id],
        );
      });

      Logger.debug('Deleted category with id: $id');
      return const Right(null);
    }, 'Failed to delete category');
  }

  @override
  Future<Either<Failure, Category>> getDefaultCategory() async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;
      final maps = await db.query(
        'categories',
        where: 'is_default = 1',
        limit: 1,
      );

      if (maps.isEmpty) {
        throw DatabaseException('Default category not found');
      }

      final category = CategoryModel.fromMap(maps.first);
      Logger.debug('Retrieved default category: ${category.name}');
      return Right(category);
    }, 'Failed to get default category');
  }

  @override
  Future<Either<Failure, List<Category>>> searchCategories(String query) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;
      final maps = await db.query(
        'categories',
        where: 'LOWER(name) LIKE LOWER(?)',
        whereArgs: ['%$query%'],
        orderBy: 'is_default DESC, name ASC',
      );

      final categories = maps.map((map) => CategoryModel.fromMap(map)).toList();
      Logger.debug('Found ${categories.length} categories matching "$query"');
      return Right(categories);
    }, 'Failed to search categories');
  }

  @override
  Future<Either<Failure, bool>> categoryNameExists(
    String name, {
    int? excludeId,
  }) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;
      
      String whereClause = 'LOWER(name) = LOWER(?)';
      List<dynamic> whereArgs = [name];
      
      if (excludeId != null) {
        whereClause += ' AND id != ?';
        whereArgs.add(excludeId);
      }
      
      final maps = await db.query(
        'categories',
        where: whereClause,
        whereArgs: whereArgs,
        limit: 1,
      );

      final exists = maps.isNotEmpty;
      Logger.debug('Category name "$name" exists: $exists');
      return Right(exists);
    }, 'Failed to check if category name exists');
  }

  /// Execute database operation with retry mechanism
  Future<Either<Failure, T>> _executeWithRetry<T>(
    Future<Either<Failure, T>> Function() operation,
    String errorContext,
  ) async {
    const maxRetries = 3;
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        return await operation();
      } catch (e, stackTrace) {
        retryCount++;
        Logger.error('$errorContext (attempt $retryCount)', e, stackTrace);

        if (retryCount >= maxRetries) {
          if (e is DatabaseException) {
            return Left(DatabaseFailure(e.message));
          } else if (e is ValidationException) {
            return Left(ValidationFailure(e.message, e.errors));
          } else {
            return Left(DatabaseFailure('$errorContext: ${e.toString()}'));
          }
        }

        // Wait before retry
        await Future.delayed(Duration(milliseconds: 100 * retryCount));
      }
    }

    return Left(DatabaseFailure('$errorContext: Max retries exceeded'));
  }
}
