import 'package:equatable/equatable.dart';

/// Base failure class for all application failures
abstract class Failure extends Equatable {
  const Failure([List properties = const <dynamic>[]]);

  @override
  List<Object> get props => [];
}

/// Database related failures
class DatabaseFailure extends Failure {
  final String message;

  const DatabaseFailure(this.message);

  @override
  List<Object> get props => [message];
}

/// Validation related failures
class ValidationFailure extends Failure {
  final Map<String, String> errors;

  const ValidationFailure(this.errors);

  @override
  List<Object> get props => [errors];
}

/// Network related failures
class NetworkFailure extends Failure {
  final String message;

  const NetworkFailure(this.message);

  @override
  List<Object> get props => [message];
}

/// Server related failures
class ServerFailure extends Failure {
  final String message;

  const ServerFailure(this.message);

  @override
  List<Object> get props => [message];
}

/// Cache related failures
class CacheFailure extends Failure {
  final String message;

  const CacheFailure(this.message);

  @override
  List<Object> get props => [message];
}

/// General application failures
class ApplicationFailure extends Failure {
  final String message;

  const ApplicationFailure(this.message);

  @override
  List<Object> get props => [message];
}
