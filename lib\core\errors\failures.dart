import 'package:equatable/equatable.dart';

/// Base failure class for all application failures
abstract class Failure extends Equatable {
  final String message;

  const Failure(this.message, [List properties = const <dynamic>[]]);

  @override
  List<Object> get props => [message];
}

/// Database related failures
class DatabaseFailure extends Failure {
  const DatabaseFailure(super.message);
}

/// Validation related failures
class ValidationFailure extends Failure {
  final Map<String, String> errors;

  const ValidationFailure(String message, this.errors) : super(message);

  @override
  List<Object> get props => [message, errors];
}

/// Network related failures
class NetworkFailure extends Failure {
  const NetworkFailure(super.message);
}

/// Server related failures
class ServerFailure extends Failure {
  const ServerFailure(super.message);
}

/// Cache related failures
class CacheFailure extends Failure {
  const CacheFailure(super.message);
}

/// General application failures
class ApplicationFailure extends Failure {
  const ApplicationFailure(super.message);
}
