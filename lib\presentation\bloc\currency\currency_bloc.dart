import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/errors/failures.dart';
import '../../../core/utils/logger.dart';
import '../../../domain/entities/currency.dart';
import '../../../domain/usecases/currency/create_currency.dart';
import '../../../domain/usecases/currency/delete_currency.dart';
import '../../../domain/usecases/currency/get_currencies.dart';
import '../../../domain/usecases/currency/update_currency.dart';
import '../../../domain/usecases/base/usecase.dart';
import '../base/base_bloc.dart';
import 'currency_event.dart';
import 'currency_state.dart';

/// Currency BLoC that manages currency state
class CurrencyBloc extends BaseBloc<CurrencyEvent, CurrencyState> {
  final CreateCurrency _createCurrency;
  final GetCurrencies _getCurrencies;
  final UpdateCurrency _updateCurrency;
  final DeleteCurrency _deleteCurrency;

  // Retry mechanism
  static const int _maxRetries = 3;
  int _getCurrenciesRetryCount = 0;

  CurrencyBloc({
    required CreateCurrency createCurrency,
    required GetCurrencies getCurrencies,
    required UpdateCurrency updateCurrency,
    required DeleteCurrency deleteCurrency,
  })  : _createCurrency = createCurrency,
        _getCurrencies = getCurrencies,
        _updateCurrency = updateCurrency,
        _deleteCurrency = deleteCurrency,
        super(const CurrencyState()) {
    on<GetCurrenciesEvent>(_onGetCurrencies);
    on<CreateCurrencyEvent>(_onCreateCurrency);
    on<UpdateCurrencyEvent>(_onUpdateCurrency);
    on<DeleteCurrencyEvent>(_onDeleteCurrency);
    on<SearchCurrenciesEvent>(_onSearchCurrencies);
    on<ClearCurrencySearchEvent>(_onClearCurrencySearch);
    on<SelectCurrencyEvent>(_onSelectCurrency);
  }

  Future<void> _onGetCurrencies(
    GetCurrenciesEvent event,
    Emitter<CurrencyState> emit,
  ) async {
    if (_getCurrenciesRetryCount >= _maxRetries) {
      emit(
        state.copyWith(
          status: CurrencyStatus.failure,
          errorMessage: 'فشل في تحميل العملات بعد عدة محاولات',
        ),
      );
      return;
    }

    emit(state.copyWith(status: CurrencyStatus.loading));

    try {
      final result = await _getCurrencies(NoParams());

      result.fold(
        (failure) {
          _getCurrenciesRetryCount++;
          Logger.error('Failed to get currencies', failure);
          emit(
            state.copyWith(
              status: CurrencyStatus.failure,
              errorMessage: _mapFailureToMessage(failure),
            ),
          );
        },
        (currencies) {
          emit(
            state.copyWith(
              status: CurrencyStatus.success,
              currencies: currencies,
              filteredCurrencies: currencies,
              clearErrorMessage: true,
            ),
          );
          _getCurrenciesRetryCount = 0;
        },
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in getCurrencies', e, stackTrace);
      emit(
        state.copyWith(
          status: CurrencyStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onCreateCurrency(
    CreateCurrencyEvent event,
    Emitter<CurrencyState> emit,
  ) async {
    emit(state.copyWith(status: CurrencyStatus.loading));

    try {
      final result = await _createCurrency(event.currency);

      result.fold(
        (failure) => emit(
          state.copyWith(
            status: CurrencyStatus.failure,
            errorMessage: _mapFailureToMessage(failure),
          ),
        ),
        (currency) {
          final updatedCurrencies = List<Currency>.from(state.currencies)
            ..add(currency);
          emit(
            state.copyWith(
              status: CurrencyStatus.success,
              currencies: updatedCurrencies,
              filteredCurrencies: _applyFilters(
                updatedCurrencies,
                state.searchQuery,
              ),
              clearErrorMessage: true,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in createCurrency', e, stackTrace);
      emit(
        state.copyWith(
          status: CurrencyStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onUpdateCurrency(
    UpdateCurrencyEvent event,
    Emitter<CurrencyState> emit,
  ) async {
    emit(state.copyWith(status: CurrencyStatus.loading));

    try {
      final result = await _updateCurrency(event.currency);

      result.fold(
        (failure) => emit(
          state.copyWith(
            status: CurrencyStatus.failure,
            errorMessage: _mapFailureToMessage(failure),
          ),
        ),
        (updatedCurrency) {
          final updatedCurrencies = state.currencies.map((currency) {
            return currency.id == updatedCurrency.id
                ? updatedCurrency
                : currency;
          }).toList();

          emit(
            state.copyWith(
              status: CurrencyStatus.success,
              currencies: updatedCurrencies,
              filteredCurrencies: _applyFilters(
                updatedCurrencies,
                state.searchQuery,
              ),
              clearErrorMessage: true,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in updateCurrency', e, stackTrace);
      emit(
        state.copyWith(
          status: CurrencyStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onDeleteCurrency(
    DeleteCurrencyEvent event,
    Emitter<CurrencyState> emit,
  ) async {
    emit(state.copyWith(status: CurrencyStatus.loading));

    try {
      final result = await _deleteCurrency(
        DeleteCurrencyParams(currencyId: event.currencyId),
      );

      result.fold(
        (failure) => emit(
          state.copyWith(
            status: CurrencyStatus.failure,
            errorMessage: _mapFailureToMessage(failure),
          ),
        ),
        (_) {
          final updatedCurrencies = state.currencies
              .where((currency) => currency.id != event.currencyId)
              .toList();

          emit(
            state.copyWith(
              status: CurrencyStatus.success,
              currencies: updatedCurrencies,
              filteredCurrencies: _applyFilters(
                updatedCurrencies,
                state.searchQuery,
              ),
              clearErrorMessage: true,
            ),
          );
        },
      );
    } catch (e, stackTrace) {
      Logger.error('Unexpected error in deleteCurrency', e, stackTrace);
      emit(
        state.copyWith(
          status: CurrencyStatus.failure,
          errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
        ),
      );
    }
  }

  void _onSearchCurrencies(
    SearchCurrenciesEvent event,
    Emitter<CurrencyState> emit,
  ) {
    final filteredCurrencies = _applyFilters(state.currencies, event.query);
    emit(
      state.copyWith(
        searchQuery: event.query,
        filteredCurrencies: filteredCurrencies,
        isSearching: event.query.isNotEmpty,
      ),
    );
  }

  void _onClearCurrencySearch(
    ClearCurrencySearchEvent event,
    Emitter<CurrencyState> emit,
  ) {
    emit(
      state.copyWith(
        searchQuery: '',
        filteredCurrencies: state.currencies,
        isSearching: false,
      ),
    );
  }

  void _onSelectCurrency(
    SelectCurrencyEvent event,
    Emitter<CurrencyState> emit,
  ) {
    emit(state.copyWith(selectedCurrency: event.currency));
  }

  /// Apply search filters to currencies
  List<Currency> _applyFilters(List<Currency> currencies, String query) {
    if (query.isEmpty) return currencies;

    return currencies
        .where((currency) =>
            currency.name.toLowerCase().contains(query.toLowerCase()) ||
            currency.code.toLowerCase().contains(query.toLowerCase()) ||
            currency.symbol.contains(query))
        .toList();
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case DatabaseFailure:
        return 'خطأ في قاعدة البيانات: ${failure.message}';
      case ValidationFailure:
        return 'خطأ في التحقق من البيانات: ${failure.message}';
      case NetworkFailure:
        return 'خطأ في الشبكة: ${failure.message}';
      default:
        return failure.message;
    }
  }
}
