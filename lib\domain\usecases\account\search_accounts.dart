import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/errors/failures.dart';
import '../../entities/account.dart';
import '../../repositories/account_repository.dart';
import '../base/usecase.dart';

/// Use case for searching accounts by query
class SearchAccounts implements UseCase<List<Account>, SearchAccountsParams> {
  final AccountRepository _repository;

  SearchAccounts(this._repository);

  @override
  Future<Either<Failure, List<Account>>> call(
    SearchAccountsParams params,
  ) async {
    return await _repository.searchAccounts(params.query);
  }
}

/// Parameters for the SearchAccounts use case
class SearchAccountsParams extends Equatable {
  final String query;

  const SearchAccountsParams({required this.query});

  @override
  List<Object> get props => [query];
}
