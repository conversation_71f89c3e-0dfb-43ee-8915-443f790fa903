import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'core/di/service_locator.dart';
import 'core/utils/logger.dart';
import 'core/theme/app_theme.dart';
import 'core/routes/route_generator.dart';
import 'core/constants/app_constants.dart';
import 'presentation/bloc/account/account_bloc.dart';
import 'presentation/bloc/account/account_event.dart';
import 'presentation/bloc/category/category_bloc.dart';
import 'presentation/bloc/category/category_event.dart';
import 'presentation/bloc/currency/currency_bloc.dart';
import 'presentation/bloc/currency/currency_event.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize service locator
  try {
    await ServiceLocator.init();
    Logger.debug('Service locator initialized successfully');
  } catch (e, stackTrace) {
    Logger.error('Failed to initialize service locator', e, stackTrace);
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AccountBloc>(
          create:
              (context) =>
                  ServiceLocator.get<AccountBloc>()
                    ..add(const GetAccountsEvent()),
        ),
        BlocProvider<CategoryBloc>(
          create:
              (context) =>
                  ServiceLocator.get<CategoryBloc>()
                    ..add(const GetCategoriesEvent()),
        ),
        BlocProvider<CurrencyBloc>(
          create:
              (context) =>
                  ServiceLocator.get<CurrencyBloc>()
                    ..add(const GetCurrenciesEvent()),
        ),
      ],
      child: MaterialApp.router(
        title: AppConstants.appName,
        theme: AppTheme.lightTheme,
        routerConfig: RouteGenerator.router,
        // RTL support
        locale: const Locale('ar', 'SA'),
        supportedLocales: const [Locale('ar', 'SA'), Locale('en', 'US')],
        // Localization delegates
        localizationsDelegates: [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        // Remove debug banner
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
