import 'package:equatable/equatable.dart';
import '../../../domain/entities/category.dart';

/// Base class for all category events
abstract class CategoryEvent extends Equatable {
  const CategoryEvent();

  @override
  List<Object?> get props => [];
}

/// Event to get all categories
class GetCategoriesEvent extends CategoryEvent {
  const GetCategoriesEvent();
}

/// Event to create a new category
class CreateCategoryEvent extends CategoryEvent {
  final Category category;

  const CreateCategoryEvent(this.category);

  @override
  List<Object?> get props => [category];
}

/// Event to update an existing category
class UpdateCategoryEvent extends CategoryEvent {
  final Category category;

  const UpdateCategoryEvent(this.category);

  @override
  List<Object?> get props => [category];
}

/// Event to delete a category
class DeleteCategoryEvent extends CategoryEvent {
  final int categoryId;

  const DeleteCategoryEvent(this.categoryId);

  @override
  List<Object?> get props => [categoryId];
}

/// Event to search categories
class SearchCategoriesEvent extends CategoryEvent {
  final String query;

  const SearchCategoriesEvent(this.query);

  @override
  List<Object?> get props => [query];
}

/// Event to clear category search
class ClearCategorySearchEvent extends CategoryEvent {
  const ClearCategorySearchEvent();
}

/// Event to select a category
class SelectCategoryEvent extends CategoryEvent {
  final Category? category;

  const SelectCategoryEvent(this.category);

  @override
  List<Object?> get props => [category];
}
