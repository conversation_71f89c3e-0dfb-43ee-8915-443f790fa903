import 'package:dartz/dartz.dart';
import '../../entities/category.dart';
import '../../repositories/category_repository.dart';
import '../../../core/errors/failures.dart';
import '../base/usecase.dart';

/// Use case for updating an existing category
class UpdateCategory implements UseCase<Category, Category> {
  final CategoryRepository repository;

  UpdateCategory(this.repository);

  @override
  Future<Either<Failure, Category>> call(Category category) async {
    return await repository.updateCategory(category);
  }
}
