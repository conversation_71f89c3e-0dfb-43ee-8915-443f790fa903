import 'package:dartz/dartz.dart';
import '../../../core/errors/failures.dart';
import '../../entities/account.dart';
import '../../repositories/account_repository.dart';
import '../base/usecase.dart';

/// Use case for updating an existing account
class UpdateAccount implements UseCase<Account, Account> {
  final AccountRepository _repository;

  UpdateAccount(this._repository);

  @override
  Future<Either<Failure, Account>> call(Account account) async {
    return await _repository.updateAccount(account);
  }
}
