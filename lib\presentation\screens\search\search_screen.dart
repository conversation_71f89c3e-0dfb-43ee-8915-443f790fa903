import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/routes/app_routes.dart';
import '../../../domain/entities/account.dart';
import '../../bloc/account/account_bloc.dart';
import '../../bloc/account/account_event.dart';
import '../../bloc/account/account_state.dart';
import '../../widgets/account/account_card.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final _searchController = TextEditingController();
  final _focusNode = FocusNode();
  String _currentQuery = '';

  @override
  void initState() {
    super.initState();
    _focusNode.requestFocus();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: _searchController,
          focusNode: _focusNode,
          decoration: const InputDecoration(
            hintText: 'البحث في الحسابات...',
            border: InputBorder.none,
            hintStyle: TextStyle(color: Colors.white70),
          ),
          style: const TextStyle(color: Colors.white),
          onChanged: _onSearchChanged,
          onSubmitted: _onSearchSubmitted,
        ),
        actions: [
          if (_currentQuery.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _clearSearch,
              tooltip: 'مسح البحث',
            ),
        ],
      ),
      body: Column(
        children: [
          // Search filters (optional)
          if (_currentQuery.isNotEmpty) _buildSearchFilters(),
          
          // Search results
          Expanded(
            child: BlocBuilder<AccountBloc, AccountState>(
              builder: (context, state) {
                if (_currentQuery.isEmpty) {
                  return _buildEmptySearch();
                }

                if (state.status == AccountStatus.loading) {
                  return const LoadingWidget(message: 'البحث جاري...');
                }

                if (state.status == AccountStatus.failure) {
                  return CustomErrorWidget(
                    message: state.errorMessage ?? AppConstants.genericErrorMessage,
                    onRetry: () => _performSearch(_currentQuery),
                  );
                }

                final searchResults = _getSearchResults(state);

                if (searchResults.isEmpty) {
                  return _buildNoResults();
                }

                return _buildSearchResults(searchResults);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchFilters() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.filter_list,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Text(
            'البحث في: ${_currentQuery}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
          ),
          const Spacer(),
          TextButton(
            onPressed: _showAdvancedFilters,
            child: const Text('فلاتر متقدمة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptySearch() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'ابحث في حساباتك',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'أدخل اسم الشخص أو تفاصيل الحساب للبحث',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          _buildSearchSuggestions(),
        ],
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    final suggestions = ['أحمد', 'محمد', 'فاطمة', 'علي'];
    
    return Column(
      children: [
        Text(
          'اقتراحات البحث:',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: AppConstants.smallPadding),
        Wrap(
          spacing: AppConstants.smallPadding,
          children: suggestions.map((suggestion) {
            return ActionChip(
              label: Text(suggestion),
              onPressed: () => _performSearch(suggestion),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildNoResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'لا توجد نتائج',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'لم يتم العثور على حسابات تطابق "${_currentQuery}"',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: () => context.push(AppRoutes.accountForm),
            icon: const Icon(Icons.add),
            label: const Text('إضافة حساب جديد'),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults(List<Account> results) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Text(
            'النتائج (${results.length})',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.defaultPadding,
            ),
            itemCount: results.length,
            itemBuilder: (context, index) {
              final account = results[index];
              return AccountCard(
                account: account,
                onTap: () => _navigateToAccountDetails(account),
                onEdit: () => _navigateToAccountEdit(account),
                onDelete: () => _showDeleteConfirmation(account),
              );
            },
          ),
        ),
      ],
    );
  }

  List<Account> _getSearchResults(AccountState state) {
    if (_currentQuery.isEmpty) return [];
    
    return state.accounts.where((account) {
      final query = _currentQuery.toLowerCase();
      return account.name.toLowerCase().contains(query) ||
             (account.phone?.toLowerCase().contains(query) ?? false) ||
             (account.details?.toLowerCase().contains(query) ?? false);
    }).toList();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _currentQuery = query.trim();
    });
    
    if (query.trim().isNotEmpty) {
      _performSearch(query.trim());
    }
  }

  void _onSearchSubmitted(String query) {
    if (query.trim().isNotEmpty) {
      _performSearch(query.trim());
    }
  }

  void _performSearch(String query) {
    _searchController.text = query;
    setState(() {
      _currentQuery = query;
    });
    
    context.read<AccountBloc>().add(SearchAccountsEvent(query: query));
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _currentQuery = '';
    });
    _focusNode.requestFocus();
  }

  void _showAdvancedFilters() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.largePadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'فلاتر البحث المتقدمة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppConstants.largePadding),
            const Text('هذه الميزة ستكون متاحة قريباً'),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToAccountDetails(Account account) {
    context.push('${AppRoutes.accountDetails}/${account.id}');
  }

  void _navigateToAccountEdit(Account account) {
    context.push(AppRoutes.accountForm, extra: account);
  }

  void _showDeleteConfirmation(Account account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف حساب "${account.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AccountBloc>().add(DeleteAccountEvent(id: account.id!));
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
