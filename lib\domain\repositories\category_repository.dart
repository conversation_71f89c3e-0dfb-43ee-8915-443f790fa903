import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/category.dart';

/// Repository interface for Category operations
abstract class CategoryRepository {
  /// Get all categories
  Future<Either<Failure, List<Category>>> getCategories();

  /// Get a single category by ID
  Future<Either<Failure, Category>> getCategoryById(int id);

  /// Create a new category
  Future<Either<Failure, Category>> createCategory(Category category);

  /// Update an existing category
  Future<Either<Failure, Category>> updateCategory(Category category);

  /// Delete a category
  Future<Either<Failure, void>> deleteCategory(int id);

  /// Get the default category
  Future<Either<Failure, Category>> getDefaultCategory();

  /// Search categories by name
  Future<Either<Failure, List<Category>>> searchCategories(String query);

  /// Check if category name exists
  Future<Either<Failure, bool>> categoryNameExists(
    String name, {
    int? excludeId,
  });
}
