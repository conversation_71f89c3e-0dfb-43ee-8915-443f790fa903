import '../../domain/entities/account.dart';

/// Account model that extends the Account entity
class AccountModel extends Account {
  const AccountModel({
    super.id,
    required super.name,
    super.phone,
    required super.amount,
    super.details,
    required super.debtType,
    required super.date,
    required super.categoryId,
    required super.currencyId,
    super.createdAt,
    super.updatedAt,
  });

  /// Create an AccountModel from a map (e.g., from database or API)
  factory AccountModel.fromMap(Map<String, dynamic> map) {
    return AccountModel(
      id: map['id'],
      name: map['name'],
      phone: map['phone'],
      amount: map['amount'],
      details: map['details'],
      debtType: map['debt_type'],
      date: map['date'],
      categoryId: map['category_id'],
      currencyId: map['currency_id'],
      createdAt:
          map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  /// Convert this AccountModel to a map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'phone': phone,
      'amount': amount,
      'details': details,
      'debt_type': debtType,
      'date': date,
      'category_id': categoryId,
      'currency_id': currencyId,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Create a copy of this AccountModel with the given fields replaced with the new values.
  @override
  AccountModel copyWith({
    int? id,
    String? name,
    String? phone,
    double? amount,
    String? details,
    String? debtType,
    String? date,
    int? categoryId,
    int? currencyId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AccountModel(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      amount: amount ?? this.amount,
      details: details ?? this.details,
      debtType: debtType ?? this.debtType,
      date: date ?? this.date,
      categoryId: categoryId ?? this.categoryId,
      currencyId: currencyId ?? this.currencyId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
