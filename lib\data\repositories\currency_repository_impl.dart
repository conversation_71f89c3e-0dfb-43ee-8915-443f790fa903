import 'package:dartz/dartz.dart';
import '../../core/errors/exceptions.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/logger.dart';
import '../../domain/entities/currency.dart';
import '../../domain/repositories/currency_repository.dart';
import '../datasources/local/database_helper.dart';
import '../models/currency_model.dart';

/// Implementation of CurrencyRepository
class CurrencyRepositoryImpl implements CurrencyRepository {
  final DatabaseHelper _databaseHelper;

  CurrencyRepositoryImpl({
    required DatabaseHelper databaseHelper,
  }) : _databaseHelper = databaseHelper;

  @override
  Future<Either<Failure, List<Currency>>> getCurrencies() async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;
      final maps = await db.query(
        'currencies',
        orderBy: 'is_default DESC, name ASC',
      );

      final currencies = maps.map((map) => CurrencyModel.fromMap(map)).toList();
      Logger.debug('Retrieved ${currencies.length} currencies');
      return Right(currencies);
    }, 'Failed to get currencies');
  }

  @override
  Future<Either<Failure, Currency>> getCurrencyById(int id) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;
      final maps = await db.query(
        'currencies',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isEmpty) {
        throw DatabaseException('Currency with id $id not found');
      }

      final currency = CurrencyModel.fromMap(maps.first);
      Logger.debug('Retrieved currency: ${currency.name}');
      return Right(currency);
    }, 'Failed to get currency by id');
  }

  @override
  Future<Either<Failure, Currency>> createCurrency(Currency currency) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;

      // Convert Currency entity to CurrencyModel
      final currencyModel = currency is CurrencyModel
          ? currency
          : CurrencyModel(
              name: currency.name,
              code: currency.code,
              symbol: currency.symbol,
              isDefault: currency.isDefault,
            );

      // Check if currency code already exists
      final existingCurrencies = await db.query(
        'currencies',
        where: 'UPPER(code) = UPPER(?)',
        whereArgs: [currencyModel.code],
      );

      if (existingCurrencies.isNotEmpty) {
        throw ValidationException(
          'Currency code already exists',
          {'code': 'رمز العملة موجود بالفعل'},
        );
      }

      final id = await db.insert('currencies', currencyModel.toMap());
      Logger.debug('Created currency with id: $id');

      return Right(currencyModel.copyWith(id: id));
    }, 'Failed to create currency');
  }

  @override
  Future<Either<Failure, Currency>> updateCurrency(Currency currency) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;

      if (currency.id == null) {
        throw ValidationException(
          'Currency ID is required for update',
          {'id': 'معرف العملة مطلوب للتحديث'},
        );
      }

      // Convert Currency entity to CurrencyModel
      final currencyModel = currency is CurrencyModel
          ? currency
          : CurrencyModel(
              id: currency.id,
              name: currency.name,
              code: currency.code,
              symbol: currency.symbol,
              isDefault: currency.isDefault,
              createdAt: currency.createdAt,
              updatedAt: DateTime.now(),
            );

      // Check if currency code already exists (excluding current currency)
      final existingCurrencies = await db.query(
        'currencies',
        where: 'UPPER(code) = UPPER(?) AND id != ?',
        whereArgs: [currencyModel.code, currencyModel.id],
      );

      if (existingCurrencies.isNotEmpty) {
        throw ValidationException(
          'Currency code already exists',
          {'code': 'رمز العملة موجود بالفعل'},
        );
      }

      final rowsAffected = await db.update(
        'currencies',
        currencyModel.toMap(),
        where: 'id = ?',
        whereArgs: [currencyModel.id],
      );

      if (rowsAffected == 0) {
        throw DatabaseException('Currency not found or could not be updated');
      }

      Logger.debug('Updated currency with id: ${currencyModel.id}');
      return Right(currencyModel);
    }, 'Failed to update currency');
  }

  @override
  Future<Either<Failure, void>> deleteCurrency(int id) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;

      // Check if currency exists and is not default
      final currencyMaps = await db.query(
        'currencies',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (currencyMaps.isEmpty) {
        throw DatabaseException('Currency not found');
      }

      final currency = CurrencyModel.fromMap(currencyMaps.first);
      if (currency.isDefault) {
        throw ValidationException(
          'Cannot delete default currency',
          {'currency': 'لا يمكن حذف العملة الافتراضية'},
        );
      }

      // Get default currency to move accounts to
      final defaultCurrencyMaps = await db.query(
        'currencies',
        where: 'is_default = 1',
        limit: 1,
      );

      if (defaultCurrencyMaps.isEmpty) {
        throw DatabaseException('Default currency not found');
      }

      final defaultCurrency = CurrencyModel.fromMap(defaultCurrencyMaps.first);

      // Start transaction
      await db.transaction((txn) async {
        // Move all accounts from this currency to default currency
        await txn.update(
          'accounts',
          {'currency_id': defaultCurrency.id},
          where: 'currency_id = ?',
          whereArgs: [id],
        );

        // Delete the currency
        await txn.delete(
          'currencies',
          where: 'id = ?',
          whereArgs: [id],
        );
      });

      Logger.debug('Deleted currency with id: $id');
      return const Right(null);
    }, 'Failed to delete currency');
  }

  @override
  Future<Either<Failure, Currency>> getDefaultCurrency() async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;
      final maps = await db.query(
        'currencies',
        where: 'is_default = 1',
        limit: 1,
      );

      if (maps.isEmpty) {
        throw DatabaseException('Default currency not found');
      }

      final currency = CurrencyModel.fromMap(maps.first);
      Logger.debug('Retrieved default currency: ${currency.name}');
      return Right(currency);
    }, 'Failed to get default currency');
  }

  @override
  Future<Either<Failure, List<Currency>>> searchCurrencies(String query) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;
      final maps = await db.query(
        'currencies',
        where: 'LOWER(name) LIKE LOWER(?) OR LOWER(code) LIKE LOWER(?) OR symbol LIKE ?',
        whereArgs: ['%$query%', '%$query%', '%$query%'],
        orderBy: 'is_default DESC, name ASC',
      );

      final currencies = maps.map((map) => CurrencyModel.fromMap(map)).toList();
      Logger.debug('Found ${currencies.length} currencies matching "$query"');
      return Right(currencies);
    }, 'Failed to search currencies');
  }

  @override
  Future<Either<Failure, bool>> currencyCodeExists(
    String code, {
    int? excludeId,
  }) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;
      
      String whereClause = 'UPPER(code) = UPPER(?)';
      List<dynamic> whereArgs = [code];
      
      if (excludeId != null) {
        whereClause += ' AND id != ?';
        whereArgs.add(excludeId);
      }
      
      final maps = await db.query(
        'currencies',
        where: whereClause,
        whereArgs: whereArgs,
        limit: 1,
      );

      final exists = maps.isNotEmpty;
      Logger.debug('Currency code "$code" exists: $exists');
      return Right(exists);
    }, 'Failed to check if currency code exists');
  }

  @override
  Future<Either<Failure, Currency>> getCurrencyByCode(String code) async {
    return _executeWithRetry(() async {
      final db = await _databaseHelper.database;
      final maps = await db.query(
        'currencies',
        where: 'UPPER(code) = UPPER(?)',
        whereArgs: [code],
        limit: 1,
      );

      if (maps.isEmpty) {
        throw DatabaseException('Currency with code $code not found');
      }

      final currency = CurrencyModel.fromMap(maps.first);
      Logger.debug('Retrieved currency by code: ${currency.name}');
      return Right(currency);
    }, 'Failed to get currency by code');
  }

  /// Execute database operation with retry mechanism
  Future<Either<Failure, T>> _executeWithRetry<T>(
    Future<Either<Failure, T>> Function() operation,
    String errorContext,
  ) async {
    const maxRetries = 3;
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        return await operation();
      } catch (e, stackTrace) {
        retryCount++;
        Logger.error('$errorContext (attempt $retryCount)', e, stackTrace);

        if (retryCount >= maxRetries) {
          if (e is DatabaseException) {
            return Left(DatabaseFailure(e.message));
          } else if (e is ValidationException) {
            return Left(ValidationFailure(e.message, e.errors));
          } else {
            return Left(DatabaseFailure('$errorContext: ${e.toString()}'));
          }
        }

        // Wait before retry
        await Future.delayed(Duration(milliseconds: 100 * retryCount));
      }
    }

    return Left(DatabaseFailure('$errorContext: Max retries exceeded'));
  }
}
