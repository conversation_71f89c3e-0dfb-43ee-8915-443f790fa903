import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/errors/failures.dart';
import '../../repositories/account_repository.dart';
import '../base/usecase.dart';

/// Use case for deleting an account
class DeleteAccount implements UseCase<void, DeleteAccountParams> {
  final AccountRepository _repository;

  DeleteAccount(this._repository);

  @override
  Future<Either<Failure, void>> call(DeleteAccountParams params) async {
    return await _repository.deleteAccount(params.id);
  }
}

/// Parameters for the DeleteAccount use case
class DeleteAccountParams extends Equatable {
  final int id;

  const DeleteAccountParams({required this.id});

  @override
  List<Object> get props => [id];
}
