import 'package:dartz/dartz.dart';
import '../../core/errors/failures.dart';
import '../entities/currency.dart';

/// Repository interface for Currency operations
abstract class CurrencyRepository {
  /// Get all currencies
  Future<Either<Failure, List<Currency>>> getCurrencies();

  /// Get a single currency by ID
  Future<Either<Failure, Currency>> getCurrencyById(int id);

  /// Create a new currency
  Future<Either<Failure, Currency>> createCurrency(Currency currency);

  /// Update an existing currency
  Future<Either<Failure, Currency>> updateCurrency(Currency currency);

  /// Delete a currency
  Future<Either<Failure, void>> deleteCurrency(int id);

  /// Get the default currency
  Future<Either<Failure, Currency>> getDefaultCurrency();

  /// Search currencies by name or code
  Future<Either<Failure, List<Currency>>> searchCurrencies(String query);

  /// Check if currency code exists
  Future<Either<Failure, bool>> currencyCodeExists(
    String code, {
    int? excludeId,
  });

  /// Get currency by code
  Future<Either<Failure, Currency>> getCurrencyByCode(String code);
}
