import 'package:equatable/equatable.dart';
import '../../../domain/entities/currency.dart';

/// Currency status enumeration
enum CurrencyStatus {
  initial,
  loading,
  success,
  failure,
}

/// Currency state class
class CurrencyState extends Equatable {
  final CurrencyStatus status;
  final List<Currency> currencies;
  final List<Currency> filteredCurrencies;
  final Currency? selectedCurrency;
  final String? errorMessage;
  final String searchQuery;
  final bool isSearching;

  const CurrencyState({
    this.status = CurrencyStatus.initial,
    this.currencies = const [],
    this.filteredCurrencies = const [],
    this.selectedCurrency,
    this.errorMessage,
    this.searchQuery = '',
    this.isSearching = false,
  });

  @override
  List<Object?> get props => [
        status,
        currencies,
        filteredCurrencies,
        selectedCurrency,
        errorMessage,
        searchQuery,
        isSearching,
      ];

  /// Create a copy of this state with the given fields replaced with the new values.
  CurrencyState copyWith({
    CurrencyStatus? status,
    List<Currency>? currencies,
    List<Currency>? filteredCurrencies,
    Currency? selectedCurrency,
    String? errorMessage,
    String? searchQuery,
    bool? isSearching,
    bool clearErrorMessage = false,
    bool clearSelectedCurrency = false,
  }) {
    return CurrencyState(
      status: status ?? this.status,
      currencies: currencies ?? this.currencies,
      filteredCurrencies: filteredCurrencies ?? this.filteredCurrencies,
      selectedCurrency: clearSelectedCurrency ? null : (selectedCurrency ?? this.selectedCurrency),
      errorMessage: clearErrorMessage ? null : (errorMessage ?? this.errorMessage),
      searchQuery: searchQuery ?? this.searchQuery,
      isSearching: isSearching ?? this.isSearching,
    );
  }

  /// Check if the state is loading
  bool get isLoading => status == CurrencyStatus.loading;

  /// Check if the state has error
  bool get hasError => status == CurrencyStatus.failure && errorMessage != null;

  /// Check if the state is successful
  bool get isSuccess => status == CurrencyStatus.success;

  /// Check if currencies are empty
  bool get isEmpty => currencies.isEmpty;

  /// Get the default currency
  Currency? get defaultCurrency {
    try {
      return currencies.firstWhere((currency) => currency.isDefault);
    } catch (e) {
      return null;
    }
  }

  /// Get non-default currencies
  List<Currency> get customCurrencies {
    return currencies.where((currency) => !currency.isDefault).toList();
  }

  /// Get currency by code
  Currency? getCurrencyByCode(String code) {
    try {
      return currencies.firstWhere((currency) => currency.code == code);
    } catch (e) {
      return null;
    }
  }

  /// Get currency by id
  Currency? getCurrencyById(int id) {
    try {
      return currencies.firstWhere((currency) => currency.id == id);
    } catch (e) {
      return null;
    }
  }
}
